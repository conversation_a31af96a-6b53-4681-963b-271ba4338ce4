# Windows ARM64 构建控制台调试

## 📋 概述

为了便于排查 Windows ARM64 构建的问题，临时为该构建启用了控制台窗口。

## 🔧 修改内容

### 配置文件修改

**文件**: `electron-builder-win-arm64.json`

**修改位置**: 在 `win` 配置节中添加了 `"console": true`

```json
{
  "win": {
    "icon": "build/icons/icon.ico",
    "files": [
      "!ffmpeg/mac-*/**/*",
      "!ffmpeg/linux-*/**/*",
      "!ffmpeg/win-x64/**/*"
    ],
    "verifyUpdateCodeSignature": false,
    "requestedExecutionLevel": "asInvoker",
    "console": true,  // ← 新增：启用控制台窗口
    "extraResources": [
      // ...
    ]
  }
}
```

## 🎯 效果

启用 `"console": true` 后，Windows ARM64 构建版本将会：

1. **显示控制台窗口** - 应用启动时会同时显示一个控制台窗口
2. **输出调试信息** - 所有 `console.log`、`console.error` 等输出会显示在控制台中
3. **便于问题排查** - 可以实时查看应用运行时的日志和错误信息

## 🚨 注意事项

### 临时性质
- ⚠️ **这是临时修改**，仅用于调试目的
- ⚠️ **正式发布前需要移除** `"console": true` 配置
- ⚠️ **只影响 Windows ARM64 构建**，其他平台不受影响

### 用户体验影响
- 用户会看到额外的控制台窗口
- 可能会影响应用的专业外观
- 控制台窗口可以被用户关闭，但可能导致应用异常

## 🔄 如何恢复

当调试完成后，需要移除控制台配置：

```bash
# 编辑配置文件
nano electron-builder-win-arm64.json

# 删除或注释掉这一行：
# "console": true,
```

或者直接删除该行：

```json
{
  "win": {
    "icon": "build/icons/icon.ico",
    "files": [
      "!ffmpeg/mac-*/**/*",
      "!ffmpeg/linux-*/**/*",
      "!ffmpeg/win-x64/**/*"
    ],
    "verifyUpdateCodeSignature": false,
    "requestedExecutionLevel": "asInvoker",
    // "console": true,  ← 删除或注释这一行
    "extraResources": [
      // ...
    ]
  }
}
```

## 📝 构建命令

使用以下命令构建带控制台的 Windows ARM64 版本：

```bash
yarn build:windows-arm64
```

构建产物：
- 文件名：`MEEA-VIOFO-Setup-{version}-windows-arm64.exe`
- 位置：`dist/` 目录
- 特性：包含控制台窗口，便于调试

## 🔍 调试技巧

### 查看启动日志
1. 运行构建的 ARM64 版本
2. 观察控制台窗口中的启动信息
3. 注意任何错误或警告信息

### 常见调试信息
- 应用初始化过程
- 文件路径解析
- FFmpeg 和 ExifTool 路径检测
- 视频处理过程
- 错误堆栈跟踪

### 日志文件
即使启用了控制台，应用仍会将日志写入文件：
- Windows: `%APPDATA%/MEEA-VIOFO/logs/`
- 可通过应用内的日志查看器访问

## ⏰ 记录

- **创建时间**: 2025-07-31
- **目的**: 临时调试 Windows ARM64 构建问题
- **影响范围**: 仅 `build:windows-arm64` 命令
- **状态**: 临时启用，需要后续移除
