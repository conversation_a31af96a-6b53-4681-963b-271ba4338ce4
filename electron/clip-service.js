const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const { app, dialog } = require('electron');
const { windowsClipFix } = require('./windows-clip-fix');

// 获取视频分辨率
async function getVideoResolution(videoPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        console.error('获取视频分辨率失败:', err);
        reject(err);
        return;
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      if (videoStream) {
        resolve({
          width: videoStream.width,
          height: videoStream.height
        });
      } else {
        reject(new Error('未找到视频流'));
      }
    });
  });
}

// 设置FFmpeg路径
const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';
const isMac = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// 简化的FFmpeg路径获取函数
function getFFmpegPath() {
  if (isDev) {
    // 开发环境使用系统安装的FFmpeg或node_modules中的
    try {
      const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
      return ffmpegInstaller.path;
    } catch (error) {
      console.warn('未找到@ffmpeg-installer/ffmpeg，使用系统FFmpeg');
      return 'ffmpeg';
    }
  } else {
    // 生产环境直接使用打包的FFmpeg路径
    const resourcesPath = process.resourcesPath;
    const arch = process.arch === 'x64' ? 'x64' : 'arm64';
    let ffmpegPath;

    if (isWin) {
      ffmpegPath = path.join(resourcesPath, 'ffmpeg', `win-${arch}`, 'ffmpeg.exe');
    } else if (isMac) {
      ffmpegPath = path.join(resourcesPath, 'ffmpeg', `mac-${arch}`, 'ffmpeg');
    } else if (isLinux) {
      ffmpegPath = path.join(resourcesPath, 'ffmpeg', `linux-${arch}`, 'ffmpeg');
    }

    console.log('🔍 查找FFmpeg路径:', ffmpegPath);
    console.log('📁 资源路径:', resourcesPath);
    console.log('🏗️ 系统架构:', arch);
    console.log('💻 操作系统:', process.platform);

    if (ffmpegPath && fs.existsSync(ffmpegPath)) {
      console.log('✅ 找到FFmpeg文件');
      return ffmpegPath;
    } else {
      console.error('❌ 未找到打包的FFmpeg文件');
      console.error('📂 尝试列出资源目录内容:');
      try {
        const resourcesContent = fs.readdirSync(resourcesPath);
        console.error('   资源目录:', resourcesContent);

        const ffmpegDirPath = path.join(resourcesPath, 'ffmpeg');
        if (fs.existsSync(ffmpegDirPath)) {
          const ffmpegContent = fs.readdirSync(ffmpegDirPath);
          console.error('   FFmpeg目录:', ffmpegContent);
        }
      } catch (listError) {
        console.error('   无法列出目录内容:', listError.message);
      }

      // 回退到系统FFmpeg
      console.warn('⚠️ 回退到系统FFmpeg');
      return 'ffmpeg';
    }
  }
}

// 简化的FFmpeg初始化 - 直接设置正确的路径
let ffmpegPath;
let windowsFFmpegWrapper = null;

try {
  ffmpegPath = getFFmpegPath();
  console.log('🔧 FFmpeg路径:', ffmpegPath);

  // 验证FFmpeg文件是否存在
  if (ffmpegPath && fs.existsSync(ffmpegPath)) {
    console.log('✅ FFmpeg文件存在，大小:', fs.statSync(ffmpegPath).size, 'bytes');

    // Windows平台额外检查
    if (isWin && !isDev) {
      try {
        // 检查文件是否为有效的PE可执行文件
        const buffer = fs.readFileSync(ffmpegPath, { start: 0, end: 2 });
        if (buffer[0] === 0x4D && buffer[1] === 0x5A) { // MZ header
          console.log('✅ FFmpeg是有效的Windows可执行文件');

          // 初始化Windows包装器作为备用
          try {
            windowsClipFix.initialize();
            if (windowsClipFix.isAvailable()) {
              windowsFFmpegWrapper = windowsClipFix.createFFmpegWrapper();
              console.log('✅ Windows FFmpeg包装器已准备作为备用');
            }
          } catch (wrapperError) {
            console.warn('⚠️ Windows包装器初始化失败，将使用标准方法:', wrapperError.message);
          }
        } else {
          console.error('❌ FFmpeg文件不是有效的Windows可执行文件');
          throw new Error('FFmpeg文件损坏');
        }
      } catch (checkError) {
        console.error('❌ FFmpeg文件检查失败:', checkError.message);
        throw checkError;
      }
    }

    // 设置FFmpeg路径
    ffmpeg.setFfmpegPath(ffmpegPath);
    console.log('✅ FFmpeg初始化成功');

  } else {
    console.error('❌ FFmpeg文件不存在:', ffmpegPath);
    throw new Error(`FFmpeg文件不存在: ${ffmpegPath}`);
  }

} catch (error) {
  console.error('❌ FFmpeg初始化失败:', error.message);

  // Windows平台显示用户友好的错误信息
  if (isWin && !isDev) {
    console.error('💡 Windows用户解决方案:');
    console.error('   1. 重新安装应用程序');
    console.error('   2. 安装Microsoft Visual C++ Redistributable');
    console.error('   3. 将应用添加到防病毒软件白名单');
    console.error('   4. 以管理员权限运行应用');
    console.error('   5. 检查磁盘空间是否充足');
  }

  throw error;
}

/**
 * Windows特定的截图函数 - 使用包装器
 */
async function captureFrameWindows(videoPath, timestamp, options = {}) {
  if (!windowsFFmpegWrapper) {
    throw new Error('Windows FFmpeg包装器不可用');
  }

  const {
    width,
    height,
    quality = 95,
    outputPath,
    returnBase64 = false
  } = options;

  // 获取视频分辨率
  let finalWidth = width || 1920;
  let finalHeight = height || 1080;

  if (options.autoDetectResolution !== false) {
    try {
      const probeArgs = ['-v', 'quiet', '-print_format', 'json', '-show_streams', videoPath];
      const probeResult = await windowsFFmpegWrapper.executeFFprobe(probeArgs);
      const metadata = JSON.parse(probeResult.stdout);

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      if (videoStream) {
        finalWidth = width || videoStream.width || 1920;
        finalHeight = height || videoStream.height || 1080;
      }
    } catch (error) {
      console.warn('获取视频分辨率失败，使用默认值:', error.message);
    }
  }

  // 设置输出路径
  let finalOutputPath = outputPath;
  let tempFilePath = null;

  if (returnBase64) {
    const paths = windowsFFmpegWrapper.getPaths();
    tempFilePath = path.join(paths.tempDir, `temp_screenshot_${Date.now()}.png`);
    finalOutputPath = tempFilePath;
  } else if (!finalOutputPath) {
    // 使用默认路径
    const videoDir = path.dirname(videoPath);
    const screenshotsDir = path.join(videoDir, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    const videoName = path.basename(videoPath, path.extname(videoPath));
    const timeStr = Math.floor(timestamp / 60).toString().padStart(2, '0') +
                   Math.floor(timestamp % 60).toString().padStart(2, '0');
    finalOutputPath = path.join(screenshotsDir, `${videoName}_${timeStr}s.png`);
  }

  console.log(`🪟 Windows截图: ${videoPath} at ${timestamp}s -> ${finalOutputPath} (${finalWidth}x${finalHeight})`);

  // 构建FFmpeg参数
  const ffmpegArgs = [
    '-ss', timestamp.toString(),
    '-i', videoPath,
    '-frames:v', '1',
    '-s', `${finalWidth}x${finalHeight}`,
    '-q:v', '1',
    '-pix_fmt', 'yuvj420p',
    '-f', 'image2',
    '-vf', 'scale=iw:ih:flags=lanczos',
    '-y', // 覆盖输出文件
    finalOutputPath
  ];

  try {
    await windowsFFmpegWrapper.executeFFmpeg(ffmpegArgs);
    console.log(`✅ Windows截图完成: ${finalOutputPath}`);

    // 处理base64返回
    if (returnBase64) {
      const imageBuffer = fs.readFileSync(finalOutputPath);
      const base64Data = imageBuffer.toString('base64');
      const mimeType = 'image/png';
      const base64String = `data:${mimeType};base64,${base64Data}`;

      // 删除临时文件
      if (fs.existsSync(finalOutputPath)) {
        fs.unlinkSync(finalOutputPath);
      }

      return {
        success: true,
        base64: base64String,
        timestamp: timestamp,
        videoPath: videoPath,
        videoName: path.basename(videoPath),
        resolution: {
          width: finalWidth,
          height: finalHeight
        }
      };
    } else {
      return {
        success: true,
        outputPath: finalOutputPath,
        tempFilePath: tempFilePath,
        timestamp: timestamp,
        videoPath: videoPath,
        videoName: path.basename(videoPath),
        resolution: {
          width: finalWidth,
          height: finalHeight
        }
      };
    }
  } catch (error) {
    console.error('🪟 Windows截图失败:', error.message);
    throw new Error(`Windows截图失败: ${error.message}`);
  }
}

/**
 * 处理Windows FFmpeg EFTYPE错误
 */
function handleWindowsFFmpegError(error, operation) {
  console.error(`🚨 Windows ${operation}操作失败:`, error.message);

  if (error.message.includes('EFTYPE') || error.code === 'EFTYPE') {
    console.error('🔍 EFTYPE错误分析:');
    console.error('   这是Windows特有的错误，通常由以下原因引起:');
    console.error('   1. FFmpeg可执行文件损坏或不完整');
    console.error('   2. 缺少Visual C++运行时库');
    console.error('   3. 防病毒软件阻止执行');
    console.error('   4. 文件权限不足');
    console.error('   5. 系统架构不匹配 (x64/ARM64)');

    console.error('💡 建议解决方案:');
    console.error('   1. 重新安装应用程序');
    console.error('   2. 安装Microsoft Visual C++ Redistributable');
    console.error('   3. 将应用添加到防病毒软件白名单');
    console.error('   4. 以管理员权限运行应用');

    return new Error(`Windows ${operation}失败: FFmpeg执行错误 (EFTYPE)。请尝试重新安装应用或联系技术支持。`);
  }

  return new Error(`Windows ${operation}失败: ${error.message}`);
}

/**
 * 截取视频帧为图片
 * @param {string} videoPath 视频文件路径
 * @param {number} timestamp 时间戳（秒）
 * @param {Object} options 选项
 * @returns {Promise<Object>} 结果对象
 */
async function captureFrame(videoPath, timestamp, options = {}) {
  // 优先使用标准方法，失败时在Windows上尝试包装器
  try {
    return await captureFrameStandard(videoPath, timestamp, options);
  } catch (error) {
    console.error('标准截图方法失败:', error.message);

    // Windows平台尝试使用包装器作为备用
    if (isWin && !isDev && windowsFFmpegWrapper) {
      console.log('🔄 尝试使用Windows包装器作为备用方案...');
      try {
        return await captureFrameWindows(videoPath, timestamp, options);
      } catch (wrapperError) {
        console.error('Windows包装器也失败了:', wrapperError.message);
      }
    }

    // 如果是Windows平台且是EFTYPE错误，提供友好提示
    if (isWin && (error.message.includes('EFTYPE') || error.code === 'EFTYPE')) {
      throw new Error(`截图功能暂时不可用。请尝试以下解决方案：
1. 重新安装应用程序
2. 安装Microsoft Visual C++ Redistributable
3. 将应用添加到防病毒软件白名单
4. 以管理员权限运行应用

如问题持续，请联系技术支持。`);
    }

    throw error;
  }
}

// 标准截图方法（重命名原来的函数）
async function captureFrameStandard(videoPath, timestamp, options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const {
        width,
        height,
        quality = 95,
        outputPath,
        showSaveDialog = false,
        createTempFile = false,
        autoDetectResolution = true,
        returnBase64 = false  // 新增：是否返回base64数据
      } = options;

      // 检查输入文件是否存在
      if (!fs.existsSync(videoPath)) {
        throw new Error(`视频文件不存在: ${videoPath}`);
      }

      // 自动检测视频分辨率
      let finalWidth = width;
      let finalHeight = height;

      if (autoDetectResolution && (!width || !height)) {
        try {
          const videoInfo = await getVideoInfo(videoPath);
          if (videoInfo.width && videoInfo.height) {
            finalWidth = videoInfo.width;
            finalHeight = videoInfo.height;
            console.log(`自动检测到视频分辨率: ${finalWidth}x${finalHeight}`);
          } else {
            // 如果无法检测，使用默认高分辨率
            finalWidth = width || 1920;
            finalHeight = height || 1080;
          }
        } catch (error) {
          console.warn('无法检测视频分辨率，使用默认值:', error.message);
          finalWidth = width || 1920;
          finalHeight = height || 1080;
        }
      } else {
        finalWidth = width || 1920;
        finalHeight = height || 1080;
      }

      // 确定输出路径
      let finalOutputPath = outputPath;
      let tempFilePath = null;

      if (returnBase64) {
        // 如果需要返回base64，创建临时文件但稍后会删除
        let tempDir;
        if (isWin && !isDev && windowsClipFix.isAvailable()) {
          // Windows使用修复后的临时目录
          const paths = windowsClipFix.getFFmpegPaths();
          tempDir = paths.tempDir;
        } else {
          tempDir = path.join(app.getPath('temp'), 'meea-screenshots');
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }
        }
        tempFilePath = path.join(tempDir, `temp_screenshot_${Date.now()}.png`);
        finalOutputPath = tempFilePath;
      } else if (createTempFile) {
        // 创建临时文件用于预览
        let tempDir;
        if (isWin && !isDev && windowsClipFix.isAvailable()) {
          // Windows使用修复后的临时目录
          const paths = windowsClipFix.getFFmpegPaths();
          tempDir = paths.tempDir;
        } else {
          tempDir = path.join(app.getPath('temp'), 'meea-screenshots');
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }
        }
        tempFilePath = path.join(tempDir, `temp_screenshot_${Date.now()}.png`);
        finalOutputPath = tempFilePath;
      } else if (!finalOutputPath) {
        if (showSaveDialog) {
          // 显示保存对话框
          const result = await dialog.showSaveDialog({
            title: '保存截图',
            defaultPath: `screenshot_${Date.now()}.png`,
            filters: [
              { name: '图片文件', extensions: ['png', 'jpg', 'jpeg'] },
              { name: '所有文件', extensions: ['*'] }
            ]
          });

          if (result.canceled) {
            resolve({ success: false, error: '用户取消保存' });
            return;
          }

          finalOutputPath = result.filePath;
        } else {
          // 使用默认路径（视频文件同目录下的screenshots文件夹）
          const videoDir = path.dirname(videoPath);
          const screenshotsDir = path.join(videoDir, 'screenshots');
          if (!fs.existsSync(screenshotsDir)) {
            fs.mkdirSync(screenshotsDir, { recursive: true });
          }

          const videoName = path.basename(videoPath, path.extname(videoPath));
          const timeStr = Math.floor(timestamp / 60).toString().padStart(2, '0') +
                         Math.floor(timestamp % 60).toString().padStart(2, '0');
          finalOutputPath = path.join(screenshotsDir, `${videoName}_${timeStr}s.png`);
        }
      }

      console.log(`开始截取视频帧: ${videoPath} at ${timestamp}s -> ${finalOutputPath} (${finalWidth}x${finalHeight})`);

      ffmpeg(videoPath)
        .seekInput(timestamp)
        .frames(1)
        .size(`${finalWidth}x${finalHeight}`)
        .outputOptions([
          '-q:v', '1',  // 使用最高质量 (1是最高质量)
          '-pix_fmt', 'yuvj420p',  // 使用全范围YUV格式以保持更好的色彩
          '-f', 'image2',
          '-vf', 'scale=iw:ih:flags=lanczos' // 保持原始宽高比，使用高质量缩放
        ])
        .output(finalOutputPath)
        .on('end', () => {
          console.log(`截图完成: ${finalOutputPath}`);

          // 如果需要返回base64数据
          if (returnBase64) {
            try {
              const imageBuffer = fs.readFileSync(finalOutputPath);
              const base64Data = imageBuffer.toString('base64');
              const mimeType = 'image/png';
              const base64String = `data:${mimeType};base64,${base64Data}`;

              // 删除临时文件
              if (fs.existsSync(finalOutputPath)) {
                fs.unlinkSync(finalOutputPath);
              }

              resolve({
                success: true,
                base64: base64String,
                timestamp: timestamp,
                videoPath: videoPath,
                videoName: path.basename(videoPath),
                resolution: {
                  width: finalWidth,
                  height: finalHeight
                }
              });
            } catch (error) {
              console.error('读取截图文件失败:', error);
              reject(new Error(`读取截图文件失败: ${error.message}`));
            }
          } else {
            resolve({
              success: true,
              outputPath: finalOutputPath,
              tempFilePath: tempFilePath,
              timestamp: timestamp,
              videoPath: videoPath,
              videoName: path.basename(videoPath),
              resolution: {
                width: finalWidth,
                height: finalHeight
              }
            });
          }
        })
        .on('error', (err) => {
          console.error(`截图失败: ${err.message}`);

          // Windows特定错误处理
          if (isWin && !isDev) {
            console.error('Windows截图错误详情:');
            console.error('  FFmpeg路径:', ffmpegPath);
            console.error('  输入文件:', videoPath);
            console.error('  输出文件:', finalOutputPath);
            console.error('  输入文件存在:', fs.existsSync(videoPath));
            console.error('  FFmpeg存在:', fs.existsSync(ffmpegPath));

            if (windowsClipFix.isAvailable()) {
              windowsClipFix.logDiagnosticInfo();
            }
          }

          reject(new Error(`截图失败: ${err.message}`));
        })
        .run();

    } catch (error) {
      console.error('截图处理失败:', error);
      reject(error);
    }
  });
}

/**
 * Windows特定的剪辑函数 - 使用包装器
 */
async function clipVideoWindows(params) {
  if (!windowsFFmpegWrapper) {
    throw new Error('Windows FFmpeg包装器不可用');
  }

  const {
    inputPath,
    startTime,
    endTime,
    outputPath,
    onProgress
  } = params;

  // 检查输入文件是否存在
  if (!fs.existsSync(inputPath)) {
    throw new Error(`视频文件不存在: ${inputPath}`);
  }

  // 验证时间参数
  if (startTime >= endTime) {
    throw new Error('结束时间必须大于开始时间');
  }

  const duration = endTime - startTime;
  let finalOutputPath = outputPath;

  if (!finalOutputPath) {
    // 生成默认输出路径
    const inputDir = path.dirname(inputPath);
    const inputName = path.basename(inputPath, path.extname(inputPath));
    const inputExt = path.extname(inputPath);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    finalOutputPath = path.join(inputDir, `${inputName}_clip_${timestamp}${inputExt}`);
  }

  console.log(`🪟 Windows剪辑: ${inputPath} (${startTime}s-${endTime}s) -> ${finalOutputPath}`);

  // 构建FFmpeg参数
  const ffmpegArgs = [
    '-ss', startTime.toString(),
    '-i', inputPath,
    '-t', duration.toString(),
    '-c:v', 'libx264',
    '-c:a', 'aac',
    '-preset', 'fast',
    '-crf', '23',
    '-y', // 覆盖输出文件
    finalOutputPath
  ];

  try {
    // 使用包装器执行FFmpeg
    const result = await windowsFFmpegWrapper.executeFFmpeg(ffmpegArgs, {
      // 可以在这里添加进度回调处理
      onProgress: onProgress
    });

    console.log(`✅ Windows剪辑完成: ${finalOutputPath}`);

    return {
      success: true,
      outputPath: finalOutputPath,
      startTime: startTime,
      endTime: endTime,
      duration: duration
    };
  } catch (error) {
    console.error('🪟 Windows剪辑失败:', error.message);
    throw new Error(`Windows剪辑失败: ${error.message}`);
  }
}

/**
 * 剪辑视频片段
 * @param {Object} params 剪辑参数
 * @returns {Promise<Object>} 结果对象
 */
async function clipVideo(params) {
  // 优先使用标准方法，失败时在Windows上尝试包装器
  try {
    return await clipVideoStandard(params);
  } catch (error) {
    console.error('标准剪辑方法失败:', error.message);

    // Windows平台尝试使用包装器作为备用
    if (isWin && !isDev && windowsFFmpegWrapper) {
      console.log('🔄 尝试使用Windows包装器作为备用方案...');
      try {
        return await clipVideoWindows(params);
      } catch (wrapperError) {
        console.error('Windows包装器也失败了:', wrapperError.message);
      }
    }

    // 如果是Windows平台且是EFTYPE错误，提供友好提示
    if (isWin && (error.message.includes('EFTYPE') || error.code === 'EFTYPE')) {
      throw new Error(`剪辑功能暂时不可用。请尝试以下解决方案：
1. 重新安装应用程序
2. 安装Microsoft Visual C++ Redistributable
3. 将应用添加到防病毒软件白名单
4. 以管理员权限运行应用

如问题持续，请联系技术支持。`);
    }

    throw error;
  }
}

// 标准剪辑方法（重命名原来的函数）
async function clipVideoStandard(params) {
  return new Promise(async (resolve, reject) => {
    try {
      const {
        inputPath,
        startTime,
        endTime,
        outputPath,
        onProgress
      } = params;

      // 检查输入文件是否存在
      if (!fs.existsSync(inputPath)) {
        throw new Error(`视频文件不存在: ${inputPath}`);
      }

      // 验证时间参数
      if (startTime >= endTime) {
        throw new Error('结束时间必须大于开始时间');
      }

      // 确定输出路径
      let finalOutputPath = outputPath;
      if (!finalOutputPath) {
        // 显示保存对话框
        const result = await dialog.showSaveDialog({
          title: '保存剪辑视频',
          defaultPath: `clip_${Date.now()}.mp4`,
          filters: [
            { name: '视频文件', extensions: ['mp4', 'avi', 'mov', 'mkv'] },
            { name: '所有文件', extensions: ['*'] }
          ]
        });

        if (result.canceled) {
          resolve({ success: false, error: '用户取消保存' });
          return;
        }

        finalOutputPath = result.filePath;
      }

      const duration = endTime - startTime;
      console.log(`开始剪辑视频: ${inputPath} (${startTime}s - ${endTime}s) -> ${finalOutputPath}`);

      ffmpeg(inputPath)
        .seekInput(startTime)
        .duration(duration)
        .outputOptions([
          '-c:v', 'libx264',  // 视频编码器
          '-c:a', 'aac',      // 音频编码器
          '-preset', 'fast',   // 编码预设
          '-crf', '23'        // 质量设置
        ])
        .output(finalOutputPath)
        .on('start', (commandLine) => {
          console.log('FFmpeg命令:', commandLine);
        })
        .on('progress', (progress) => {
          // 计算进度百分比
          const percent = Math.min(100, Math.max(0, (progress.timemark ? 
            (parseFloat(progress.timemark.replace(':', '')) / duration) * 100 : 0)));
          
          console.log(`剪辑进度: ${percent.toFixed(1)}%`);
          
          if (onProgress) {
            onProgress(percent);
          }
        })
        .on('end', () => {
          console.log(`剪辑完成: ${finalOutputPath}`);
          resolve({
            success: true,
            outputPath: finalOutputPath,
            startTime: startTime,
            endTime: endTime,
            duration: duration
          });
        })
        .on('error', (err) => {
          console.error(`剪辑失败: ${err.message}`);

          // Windows特定错误处理
          if (isWin && !isDev) {
            console.error('Windows剪辑错误详情:');
            console.error('  FFmpeg路径:', ffmpegPath);
            console.error('  输入文件:', inputPath);
            console.error('  输出文件:', finalOutputPath);
            console.error('  输入文件存在:', fs.existsSync(inputPath));
            console.error('  FFmpeg存在:', fs.existsSync(ffmpegPath));

            if (windowsClipFix.isAvailable()) {
              windowsClipFix.logDiagnosticInfo();
            }
          }

          reject(new Error(`剪辑失败: ${err.message}`));
        })
        .run();

    } catch (error) {
      console.error('剪辑处理失败:', error);
      reject(error);
    }
  });
}

/**
 * Windows专用获取视频信息
 */
async function getVideoInfoWindows(videoPath) {
  if (!windowsFFmpegWrapper) {
    throw new Error('Windows FFmpeg包装器不可用');
  }

  const probeArgs = ['-v', 'quiet', '-print_format', 'json', '-show_streams', '-show_format', videoPath];

  try {
    const result = await windowsFFmpegWrapper.executeFFprobe(probeArgs);
    const metadata = JSON.parse(result.stdout);

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

    return {
      duration: metadata.format.duration,
      size: metadata.format.size,
      bitrate: metadata.format.bit_rate,
      video: videoStream ? {
        codec: videoStream.codec_name,
        width: videoStream.width,
        height: videoStream.height,
        fps: eval(videoStream.r_frame_rate) // 计算帧率
      } : null,
      audio: audioStream ? {
        codec: audioStream.codec_name,
        sampleRate: audioStream.sample_rate,
        channels: audioStream.channels
      } : null
    };
  } catch (error) {
    console.error('🪟 Windows获取视频信息失败:', error.message);
    throw new Error(`Windows获取视频信息失败: ${error.message}`);
  }
}

/**
 * 获取视频信息
 * @param {string} videoPath 视频文件路径
 * @returns {Promise<Object>} 视频信息
 */
async function getVideoInfo(videoPath) {
  // Windows平台强制使用专用函数
  if (isWin && !isDev) {
    if (windowsFFmpegWrapper) {
      return await getVideoInfoWindows(videoPath);
    } else {
      throw new Error('Windows视频信息获取功能不可用：FFmpeg初始化失败。请尝试重新安装应用或联系技术支持。');
    }
  }

  // 非Windows平台使用fluent-ffmpeg
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

      resolve({
        duration: metadata.format.duration,
        size: metadata.format.size,
        bitrate: metadata.format.bit_rate,
        video: videoStream ? {
          codec: videoStream.codec_name,
          width: videoStream.width,
          height: videoStream.height,
          fps: eval(videoStream.r_frame_rate) // 计算帧率
        } : null,
        audio: audioStream ? {
          codec: audioStream.codec_name,
          sampleRate: audioStream.sample_rate,
          channels: audioStream.channels
        } : null
      });
    });
  });
}

// 多视频合成剪辑
async function clipMultiVideo(options) {
  const {
    videoFiles: originalVideoFiles,
    startTime,
    endTime,
    layout = 'auto',
    showSaveDialog = true,
    onProgress,
    preserveOrder = false  // 新增：是否保持传入的视频顺序
  } = options;

  if (!originalVideoFiles || originalVideoFiles.length === 0) {
    throw new Error('没有提供视频文件');
  }

  // 根据 preserveOrder 决定是否重新排序
  let videoFiles;
  if (preserveOrder) {
    // 保持传入的顺序
    videoFiles = [...originalVideoFiles];
    console.log('🔄 多视频剪辑 - 保持用户指定的视频顺序');
  } else {
    // 按照与MultiVideoPlayer相同的逻辑排序视频文件
    videoFiles = sortVideoFilesForDisplay(originalVideoFiles);
    console.log('🔄 多视频剪辑 - 使用默认摄像头角度排序');
  }

  console.log('📋 多视频剪辑 - 最终视频处理顺序:');
  videoFiles.forEach((file, index) => {
    const angle = getCameraAngle(file.name);
    console.log(`  ${index}: ${file.name} (${angle})`);
  });

  const duration = endTime - startTime;
  if (duration <= 0) {
    throw new Error('无效的时间范围');
  }

  return new Promise(async (resolve, reject) => {
    try {
      let outputPath;

      if (showSaveDialog) {
        const result = await dialog.showSaveDialog({
          title: '保存多视频剪辑',
          defaultPath: `多视频剪辑_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.mp4`,
          filters: [
            { name: '视频文件', extensions: ['mp4'] }
          ]
        });

        if (result.canceled) {
          resolve({ success: false, cancelled: true });
          return;
        }

        outputPath = result.filePath;
      } else {
        // 自动生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const downloadsPath = app.getPath('downloads');
        outputPath = path.join(downloadsPath, `多视频剪辑_${timestamp}.mp4`);
      }

      console.log(`开始多视频合成剪辑: ${videoFiles.length}个视频 -> ${outputPath}`);

      // 获取视频分辨率信息
      const getVideoResolution = async (videoPath) => {
        return new Promise((resolve, reject) => {
          ffmpeg.ffprobe(videoPath, (err, metadata) => {
            if (err) {
              reject(err);
              return;
            }

            const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
            if (!videoStream) {
              reject(new Error('未找到视频流'));
              return;
            }

            resolve({
              width: videoStream.width,
              height: videoStream.height,
              aspectRatio: videoStream.width / videoStream.height
            });
          });
        });
      };

      // 检测所有视频分辨率
      console.log('检测所有视频分辨率...');
      const videoResolutions = [];
      for (let i = 0; i < videoFiles.length; i++) {
        console.log(`检测视频 ${i} 分辨率: ${videoFiles[i].path}`);
        const resolution = await getVideoResolution(videoFiles[i].path);
        videoResolutions.push(resolution);
        console.log(`视频 ${i} 分辨率: ${JSON.stringify(resolution)}`);
      }

      // 打印所有视频分辨率
      console.log('剪辑 - 所有视频分辨率:');
      videoResolutions.forEach((res, index) => {
        console.log(`  视频 ${index}: ${res.width}x${res.height} (比例: ${res.aspectRatio.toFixed(2)})`);
      });

      // 使用统一的布局配置函数，移除局部函数定义
      const layoutConfig = getVideoLayoutConfig(videoFiles.length, videoResolutions);


      console.log('多视频剪辑布局配置:');
      console.log(`  最终分辨率: ${JSON.stringify(layoutConfig.resolution)}`);
      console.log(`  FFmpeg滤镜: ${layoutConfig.filter}`);

      // 创建FFmpeg命令
      const command = ffmpeg();

      // 添加所有输入视频
      videoFiles.forEach(file => {
        command.input(file.path)
          .seekInput(startTime)
          .duration(duration);
      });

      // 添加第一个视频的音频
      command.input(videoFiles[0].path)
        .seekInput(startTime)
        .duration(duration);

      command
        .complexFilter([
          layoutConfig.filter
        ])
        .outputOptions([
          '-map', layoutConfig.map,
          '-map', `${videoFiles.length}:a`, // 使用第一个视频的音频
          '-c:v', 'libx264',
          '-c:a', 'aac',
          '-preset', 'fast',
          '-crf', '23',
          '-r', '30' // 设置帧率
        ])
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('FFmpeg命令:', commandLine);
        })
        .on('progress', (progress) => {
          const percent = Math.min(100, Math.max(0, progress.percent || 0));
          console.log(`多视频合成进度: ${percent.toFixed(1)}%`);

          if (onProgress) {
            onProgress(percent);
          }
        })
        .on('end', () => {
          console.log(`多视频合成完成: ${outputPath}`);
          resolve({
            success: true,
            outputPath: outputPath,
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            videoCount: videoFiles.length
          });
        })
        .on('error', (err) => {
          console.error(`多视频合成失败: ${err.message}`);
          reject(new Error(`多视频合成失败: ${err.message}`));
        })
        .run();

    } catch (error) {
      console.error('多视频合成剪辑错误:', error);
      reject(error);
    }
  });
}

// 获取摄像头角度信息
function getCameraAngle(filename) {
  const name = filename.toLowerCase();
  if (name.includes('f.mp4') || name.includes('front')) {
    return 'F'; // 前摄像头
  } else if (name.includes('r.mp4') || name.includes('rear')) {
    return 'R'; // 后摄像头
  } else if (name.includes('i.mp4') || name.includes('interior') || name.includes('inside')) {
    return 'I'; // 内摄像头
  }
  return 'unknown';
}

// 按照与MultiVideoPlayer相同的逻辑排序视频文件
function sortVideoFilesForDisplay(videoFiles) {
  const sorted = [...videoFiles];

  if (videoFiles.length === 2) {
    // 两个视频：按照前(F)、后(R)、内(I)的优先顺序排列
    const angleOrder = { 'F': 0, 'R': 1, 'I': 2, 'unknown': 3 };

    sorted.sort((a, b) => {
      const angleA = getCameraAngle(a.name);
      const angleB = getCameraAngle(b.name);
      return angleOrder[angleA] - angleOrder[angleB];
    });
  } else {
    // 三个或更多视频：按照后(R)、内(I)、前(F)的顺序排列
    const angleOrder = { 'R': 0, 'I': 1, 'F': 2, 'unknown': 3 };

    sorted.sort((a, b) => {
      const angleA = getCameraAngle(a.name);
      const angleB = getCameraAngle(b.name);
      return angleOrder[angleA] - angleOrder[angleB];
    });
  }

  return sorted;
}

/**
 * 获取视频布局配置 - 智能计算最终尺寸
 * @param {number} count 视频数量
 * @param {Array} videoResolutions 视频分辨率数组（按实际顺序）
 * @returns {Object} 布局配置
 */
function getVideoLayoutConfig(count, videoResolutions = []) {
  if (count === 1) {
    const video = videoResolutions[0];
    return {
      filter: `[0:v]scale=${video.width}:${video.height}[v0]`,
      map: '[v0]',
      resolution: { width: video.width, height: video.height }
    };
  } else if (count === 2) {
    // 两个视频：上下布局
    // 上方视频保持原始比例，下方视频匹配上方宽度
    const topVideo = videoResolutions[0];
    const bottomVideo = videoResolutions[1];

    // 使用较大的宽度作为基准
    const finalWidth = Math.max(topVideo.width, bottomVideo.width);

    // 计算上方视频的缩放尺寸（保持比例）
    const topHeight = Math.floor(finalWidth * topVideo.height / topVideo.width);

    // 计算下方视频的缩放尺寸（保持比例）
    const bottomHeight = Math.floor(finalWidth * bottomVideo.height / bottomVideo.width);

    const finalHeight = topHeight + bottomHeight;

    return {
      filter: `[0:v]scale=${finalWidth}:${topHeight}[v0];[1:v]scale=${finalWidth}:${bottomHeight}[v1];[v0][v1]vstack=inputs=2[v]`,
      map: '[v]',
      resolution: { width: finalWidth, height: finalHeight }
    };
  } else if (count === 3) {
    // 三个视频：上方两个并排，下方一个
    const video1 = videoResolutions[0];
    const video2 = videoResolutions[1];
    const video3 = videoResolutions[2];

    // 确定最终宽度（使用最大宽度）
    const maxWidth = Math.max(video1.width, video2.width, video3.width);
    const finalWidth = maxWidth;

    // 上方两个视频各占一半宽度
    const topVideoWidth = Math.floor(finalWidth / 2);

    // 计算上方视频的高度（保持比例）
    const topVideo1Height = Math.floor(topVideoWidth * video1.height / video1.width);
    const topVideo2Height = Math.floor(topVideoWidth * video2.height / video2.width);
    const topRowHeight = Math.max(topVideo1Height, topVideo2Height);

    // 计算下方视频的高度（保持比例）
    const bottomVideoHeight = Math.floor(finalWidth * video3.height / video3.width);

    const finalHeight = topRowHeight + bottomVideoHeight;

    return {
      filter: `[0:v]scale=${topVideoWidth}:${topVideo1Height}[v0_scaled];[1:v]scale=${topVideoWidth}:${topVideo2Height}[v1_scaled];[2:v]scale=${finalWidth}:${bottomVideoHeight}[v2];[v0_scaled]pad=${topVideoWidth}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v0];[v1_scaled]pad=${topVideoWidth}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v1];[v0][v1]hstack=inputs=2[top];[top][v2]vstack=inputs=2[v]`,
      map: '[v]',
      resolution: { width: finalWidth, height: finalHeight }
    };
  } else {
    // 4个或更多视频，使用2x2网格
    const maxWidth = Math.max(...videoResolutions.map(v => v.width));
    const maxHeight = Math.max(...videoResolutions.map(v => v.height));

    // 每个视频占四分之一空间
    const videoWidth = Math.floor(maxWidth / 2);
    const videoHeight = Math.floor(maxHeight / 2);

    const finalWidth = videoWidth * 2;
    const finalHeight = videoHeight * 2;

    return {
      filter: `[0:v]scale=${videoWidth}:${videoHeight}[v0];[1:v]scale=${videoWidth}:${videoHeight}[v1];[2:v]scale=${videoWidth}:${videoHeight}[v2];[3:v]scale=${videoWidth}:${videoHeight}[v3];[v0][v1]hstack=inputs=2[top];[v2][v3]hstack=inputs=2[bottom];[top][bottom]vstack=inputs=2[v]`,
      map: '[v]',
      resolution: { width: finalWidth, height: finalHeight }
    };
  }
}

/**
 * Windows特定的多视频截图函数
 */
async function captureMultiVideoFrameWindows(options) {
  if (!windowsFFmpegWrapper) {
    throw new Error('Windows FFmpeg包装器不可用');
  }

  const {
    videoFiles: originalVideoFiles,
    timestamp,
    layout = 'auto',
    showSaveDialog = true,
    createTempFile = false,
    returnBase64 = false,
    outputPath,
    preserveOrder = false  // 新增：是否保持传入的视频顺序
  } = options;

  // 过滤有效的视频文件，并根据 preserveOrder 决定是否重新排序
  let videoFiles;
  if (preserveOrder) {
    // 保持传入的顺序，只过滤无效文件
    videoFiles = originalVideoFiles.filter(file =>
      file && file.path && fs.existsSync(file.path)
    );
    console.log('🔄 保持用户指定的视频顺序');
  } else {
    // 使用默认排序逻辑
    const filteredFiles = originalVideoFiles.filter(file =>
      file && file.path && fs.existsSync(file.path)
    );
    videoFiles = sortVideoFilesForDisplay(filteredFiles);
    console.log('🔄 使用默认摄像头角度排序');
  }

  if (videoFiles.length === 0) {
    throw new Error('没有有效的视频文件');
  }

  // 打印最终的视频顺序
  console.log('📋 最终视频处理顺序:');
  videoFiles.forEach((file, index) => {
    const angle = getCameraAngle(file.name);
    console.log(`  ${index}: ${file.name} (${angle})`);
  });

  const safeTimestamp = Math.max(0, timestamp || 0);
  const inputCount = videoFiles.length;

  // 获取视频分辨率信息
  console.log('检测所有视频分辨率...');
  const videoResolutions = [];
  for (let i = 0; i < videoFiles.length; i++) {
    try {
      const resolution = await getVideoResolution(videoFiles[i].path);
      resolution.aspectRatio = resolution.width / resolution.height;
      videoResolutions.push(resolution);
      console.log(`视频 ${i} 分辨率: ${JSON.stringify(resolution)}`);
    } catch (error) {
      console.warn(`获取视频 ${i} 分辨率失败，使用默认值:`, error.message);
      videoResolutions.push({ width: 1920, height: 1080, aspectRatio: 16/9 });
    }
  }

  // 打印所有视频分辨率
  console.log('所有视频分辨率:');
  videoResolutions.forEach((res, index) => {
    console.log(`  视频 ${index}: ${res.width}x${res.height} (比例: ${res.aspectRatio.toFixed(2)})`);
  });

  const layoutConfig = getVideoLayoutConfig(inputCount, videoResolutions);

  console.log(`🪟 Windows多视频截图: ${inputCount}个视频 at ${safeTimestamp}s`);
  console.log(`   布局: ${JSON.stringify(layoutConfig.resolution)}`);

  // 设置输出路径
  let finalOutputPath = outputPath;
  let tempFilePath = null;

  if (returnBase64 || createTempFile) {
    const paths = windowsFFmpegWrapper.getPaths();
    // 确保临时目录存在
    if (!fs.existsSync(paths.tempDir)) {
      fs.mkdirSync(paths.tempDir, { recursive: true });
      console.log('创建临时目录:', paths.tempDir);
    }
    tempFilePath = path.join(paths.tempDir, `temp_multi_screenshot_${Date.now()}.png`);
    finalOutputPath = tempFilePath;
    console.log('设置临时文件路径:', finalOutputPath);
  } else if (!finalOutputPath) {
    // 使用默认路径
    const timestamp_str = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    finalOutputPath = path.join(process.cwd(), `多视频截图_${timestamp_str}.png`);
  }

  // 构建FFmpeg参数
  const ffmpegArgs = [];

  // 添加所有输入视频
  videoFiles.forEach(file => {
    ffmpegArgs.push('-ss', safeTimestamp.toString());
    ffmpegArgs.push('-i', file.path);
  });

  // 添加滤镜和输出参数
  ffmpegArgs.push(
    '-frames:v', '1',
    '-filter_complex', layoutConfig.filter,
    '-map', layoutConfig.map,  // 使用 -map 而不是 -s
    '-q:v', '1',
    '-pix_fmt', 'yuvj420p',
    '-f', 'image2',
    '-y', // 覆盖输出文件
    finalOutputPath
  );

  try {
    console.log('🚀 执行 FFmpeg 命令...');
    console.log('FFmpeg 参数:', ffmpegArgs.join(' '));

    await windowsFFmpegWrapper.executeFFmpeg(ffmpegArgs);
    console.log(`✅ Windows多视频截图完成: ${finalOutputPath}`);

    // 验证文件是否存在
    if (!fs.existsSync(finalOutputPath)) {
      throw new Error(`截图文件未生成: ${finalOutputPath}`);
    }

    const fileStats = fs.statSync(finalOutputPath);
    console.log(`📁 截图文件信息: 大小 ${fileStats.size} 字节`);

    // 处理base64返回
    if (returnBase64) {
      const imageBuffer = fs.readFileSync(finalOutputPath);
      const base64Data = imageBuffer.toString('base64');
      const mimeType = 'image/png';
      const base64String = `data:${mimeType};base64,${base64Data}`;

      // 删除临时文件
      if (fs.existsSync(finalOutputPath)) {
        fs.unlinkSync(finalOutputPath);
        console.log('🗑️ 临时文件已删除');
      }

      return {
        success: true,
        base64: base64String,
        timestamp: safeTimestamp,
        videoCount: inputCount,
        resolution: layoutConfig.resolution,
        videoName: `多视频合成 (${inputCount}个视频)`
      };
    } else {
      return {
        success: true,
        outputPath: finalOutputPath,
        tempFilePath: tempFilePath,
        timestamp: safeTimestamp,
        videoCount: inputCount,
        resolution: layoutConfig.resolution,
        videoName: `多视频合成 (${inputCount}个视频)`
      };
    }
  } catch (error) {
    console.error('🪟 Windows多视频截图失败:', error.message);
    throw new Error(`Windows多视频截图失败: ${error.message}`);
  }
}

// 多视频合成截图
async function captureMultiVideoFrame(options) {
  // Windows平台强制使用专用函数
  if (isWin && !isDev) {
    if (windowsFFmpegWrapper) {
      try {
        return await captureMultiVideoFrameWindows(options);
      } catch (error) {
        const handledError = handleWindowsFFmpegError(error, '多视频截图');
        console.error('Windows专用多视频截图失败:', handledError.message);
        throw handledError; // 不再回退，直接抛出错误
      }
    } else {
      // Windows上没有可用的FFmpeg包装器
      throw new Error('Windows多视频截图功能不可用：FFmpeg初始化失败。请尝试重新安装应用或联系技术支持。');
    }
  }
  const {
    videoFiles: originalVideoFiles,
    timestamp,
    layout = 'auto',
    showSaveDialog = true,
    createTempFile = false,  // 是否创建临时预览文件
    quality = 100,  // 使用最高质量
    returnBase64 = false,  // 新增：是否返回base64数据
    preserveOrder = false  // 新增：是否保持传入的视频顺序
  } = options;

  console.log('多视频合成截图开始，参数:', {
    videoCount: originalVideoFiles?.length,
    timestamp,
    showSaveDialog,
    createTempFile,
    quality
  });

  if (!originalVideoFiles || originalVideoFiles.length === 0) {
    throw new Error('没有提供视频文件');
  }

  // 根据 preserveOrder 决定是否重新排序
  let videoFiles;
  if (preserveOrder) {
    // 保持传入的顺序
    videoFiles = [...originalVideoFiles];
    console.log('🔄 保持用户指定的视频顺序');
  } else {
    // 按照与MultiVideoPlayer相同的逻辑排序视频文件
    videoFiles = sortVideoFilesForDisplay(originalVideoFiles);
    console.log('🔄 使用默认摄像头角度排序');
  }

  // 打印最终的视频文件详细信息
  console.log('📋 最终视频处理顺序:');
  videoFiles.forEach((file, index) => {
    const angle = getCameraAngle(file.name);
    console.log(`  ${index}: ${file.name} (${angle})`);
  });

  // 验证视频文件结构
  for (let i = 0; i < videoFiles.length; i++) {
    const file = videoFiles[i];
    if (!file || !file.path) {
      throw new Error(`视频文件 ${i} 缺少路径信息: ${JSON.stringify(file)}`);
    }
    if (!fs.existsSync(file.path)) {
      throw new Error(`视频文件不存在: ${file.path}`);
    }
  }

  // 确保时间戳是有效的数字
  const safeTimestamp = Math.max(0, parseFloat(timestamp) || 0);
  console.log('使用时间戳:', safeTimestamp);

  // 获取所有视频的分辨率
  console.log('检测所有视频分辨率...');
  const videoResolutions = [];

  for (let i = 0; i < videoFiles.length; i++) {
    const videoPath = videoFiles[i].path;
    console.log(`检测视频 ${i} 分辨率:`, videoPath);

    try {
      const resolution = await getVideoResolution(videoPath);
      videoResolutions.push({
        ...resolution,
        aspectRatio: resolution.width / resolution.height
      });
      console.log(`视频 ${i} 分辨率:`, videoResolutions[i]);
    } catch (error) {
      console.error(`获取视频 ${i} 分辨率失败，使用默认分辨率:`, error);
      videoResolutions.push({
        width: 1920,
        height: 1080,
        aspectRatio: 1920 / 1080
      });
    }
  }

  // 使用第一个视频（主视频）的分辨率作为基准
  const mainResolution = videoResolutions[0];
  const mainWidth = mainResolution.width;
  const mainHeight = mainResolution.height;
  console.log('主视频分辨率:', { width: mainWidth, height: mainHeight });

  return new Promise(async (resolve, reject) => {
    try {
      let outputPath;
      let tempFilePath;

      if (returnBase64) {
        // 如果需要返回base64，创建临时文件但稍后会删除
        const tempDir = path.join(require('os').tmpdir(), 'meea-screenshots');
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        tempFilePath = path.join(tempDir, `temp_multi_screenshot_${Date.now()}.png`);
        outputPath = tempFilePath;
        console.log('创建临时多视频base64文件:', tempFilePath);
      } else if (createTempFile) {
        // 创建临时预览文件
        const tempDir = path.join(require('os').tmpdir(), 'meea-screenshots');
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        tempFilePath = path.join(tempDir, `temp_multi_screenshot_${Date.now()}.png`);
        outputPath = tempFilePath;
        console.log('创建临时多视频预览文件:', tempFilePath);
      } else if (showSaveDialog) {
        const result = await dialog.showSaveDialog({
          title: '保存多视频截图',
          defaultPath: `多视频截图_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`,
          filters: [
            { name: 'PNG图片 (无损)', extensions: ['png'] },
            { name: 'JPEG图片 (高质量)', extensions: ['jpg', 'jpeg'] }
          ]
        });

        if (result.canceled) {
          resolve({ success: false, cancelled: true });
          return;
        }

        outputPath = result.filePath;
      } else {
        // 自动生成文件名，默认使用PNG格式以获得最佳质量
        const timestamp_str = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const downloadsPath = app.getPath('downloads');
        outputPath = path.join(downloadsPath, `多视频截图_${timestamp_str}.png`);
      }

      console.log(`开始多视频合成截图: ${videoFiles.length}个视频 -> ${outputPath}`);

      // 注意：即使只有一个视频，我们也要按照多视频布局来处理
      // 这样可以保持多视频截图的一致性和布局

      // 多视频合成截图
      console.log(`开始多视频合成截图: ${videoFiles.length}个视频`);

      // 根据视频数量和分辨率计算高分辨率合成布局
      let filterComplex;
      let finalResolution;
      const scaleFlags = 'flags=lanczos'; // 使用Lanczos算法进行高质量缩放

      if (videoFiles.length === 1) {
        // 单视频：直接使用原分辨率
        finalResolution = {
          width: mainWidth,
          height: mainHeight
        };

        // 单视频不需要复杂的滤镜，直接缩放到目标分辨率
        filterComplex = `[0:v]scale=${mainWidth}:${mainHeight}:${scaleFlags}[out]`;

      } else if (videoFiles.length === 2) {
        // 2个视频布局：次视频在左上角，主视频占据下方整行
        // 按照displayFiles的顺序：[0]=主视频(下方), [1]=次视频(左上角)

        // 主视频（videoFiles[0] = displayFiles[0]）保持原有分辨率
        const mainVideoWidth = mainWidth;
        const mainVideoHeight = mainHeight;

        // 次视频（videoFiles[1] = displayFiles[1]）缩放到主视频宽度的一半
        const subVideoWidth = Math.floor(mainWidth / 2);
        const subVideoHeight = Math.floor(subVideoWidth / videoResolutions[1].aspectRatio);

        // 上排高度为次视频高度，下排高度为主视频高度
        const topRowHeight = subVideoHeight;
        const bottomRowHeight = mainVideoHeight;

        finalResolution = {
          width: mainVideoWidth,
          height: topRowHeight + bottomRowHeight
        };

        // 布局：次视频(videoFiles[1])在左上角，主视频(videoFiles[0])在下方整行
        // 上排：次视频在左侧，右侧用黑色填充
        // 下排：主视频占满整行
        filterComplex = `[1:v]scale=${subVideoWidth}:${subVideoHeight}:${scaleFlags}[v1_scaled];[0:v]scale=${mainVideoWidth}:${mainVideoHeight}:${scaleFlags}[v0_scaled];[v1_scaled]pad=${mainVideoWidth}:${topRowHeight}:0:0:black[top];[top][v0_scaled]vstack=inputs=2[out]`;

      } else if (videoFiles.length === 3) {
        // 3个视频：上面两个，下面一个
        // 按照displayFiles的顺序：[0]=后摄像头(左上), [1]=内摄像头(右上), [2]=前摄像头(下方)
        // 上排：两个视频都使用主视频宽度的一半，高度统一
        // 下排：一个视频使用主视频的完整宽度
        const topVideo1Width = Math.floor(mainWidth / 2);  // 后摄像头(videoFiles[0])
        const topVideo1Height = Math.floor(topVideo1Width / videoResolutions[0].aspectRatio);
        const topVideo2Width = Math.floor(mainWidth / 2);  // 内摄像头(videoFiles[1])
        const topVideo2Height = Math.floor(topVideo2Width / videoResolutions[1].aspectRatio);

        // 上排高度统一为两个视频中的最大高度
        const topRowHeight = Math.max(topVideo1Height, topVideo2Height);

        const bottomVideoWidth = mainWidth;
        const bottomVideoHeight = Math.floor(bottomVideoWidth / videoResolutions[2].aspectRatio);

        finalResolution = {
          width: mainWidth,
          height: topRowHeight + bottomVideoHeight
        };

        // 使用统一的上排高度，并添加背景色填充
        filterComplex = `[0:v]scale=${topVideo1Width}:${topVideo1Height}:${scaleFlags}[v0_scaled];[1:v]scale=${topVideo2Width}:${topVideo2Height}:${scaleFlags}[v1_scaled];[2:v]scale=${bottomVideoWidth}:${bottomVideoHeight}:${scaleFlags}[v2];[v0_scaled]pad=${topVideo1Width}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v0];[v1_scaled]pad=${topVideo2Width}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v1];[v0][v1]hstack=inputs=2[top];[top][v2]vstack=inputs=2[out]`;

      } else {
        // 4个或更多视频：2x2网格，只取前4个
        const videoCount = Math.min(videoFiles.length, 4);

        if (videoCount === 4) {
          // 每个视频使用主视频宽度的一半
          const singleWidth = Math.floor(mainWidth / 2);
          const video1Height = Math.floor(singleWidth / videoResolutions[0].aspectRatio);
          const video2Height = Math.floor(singleWidth / videoResolutions[1].aspectRatio);
          const video3Height = Math.floor(singleWidth / videoResolutions[2].aspectRatio);
          const video4Height = Math.floor(singleWidth / videoResolutions[3].aspectRatio);

          const topRowHeight = Math.max(video1Height, video2Height);
          const bottomRowHeight = Math.max(video3Height, video4Height);

          finalResolution = {
            width: mainWidth,
            height: topRowHeight + bottomRowHeight
          };

          // 使用统一的行高度，并添加背景色填充
          filterComplex = `[0:v]scale=${singleWidth}:${video1Height}:${scaleFlags}[v0_scaled];[1:v]scale=${singleWidth}:${video2Height}:${scaleFlags}[v1_scaled];[2:v]scale=${singleWidth}:${video3Height}:${scaleFlags}[v2_scaled];[3:v]scale=${singleWidth}:${video4Height}:${scaleFlags}[v3_scaled];[v0_scaled]pad=${singleWidth}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v0];[v1_scaled]pad=${singleWidth}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v1];[v2_scaled]pad=${singleWidth}:${bottomRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v2];[v3_scaled]pad=${singleWidth}:${bottomRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v3];[v0][v1]hstack=inputs=2[top];[v2][v3]hstack=inputs=2[bottom];[top][bottom]vstack=inputs=2[out]`;
        } else {
          // 如果少于4个，回退到3视频布局
          const topVideo1Width = Math.floor(mainWidth / 2);
          const topVideo1Height = Math.floor(topVideo1Width / videoResolutions[0].aspectRatio);
          const topVideo2Width = Math.floor(mainWidth / 2);
          const topVideo2Height = Math.floor(topVideo2Width / videoResolutions[1].aspectRatio);

          const topRowHeight = Math.max(topVideo1Height, topVideo2Height);

          const bottomVideoWidth = mainWidth;
          const bottomVideoHeight = Math.floor(bottomVideoWidth / videoResolutions[2].aspectRatio);

          finalResolution = {
            width: mainWidth,
            height: topRowHeight + bottomVideoHeight
          };

          filterComplex = `[0:v]scale=${topVideo1Width}:${topVideo1Height}:${scaleFlags}[v0_scaled];[1:v]scale=${topVideo2Width}:${topVideo2Height}:${scaleFlags}[v1_scaled];[2:v]scale=${bottomVideoWidth}:${bottomVideoHeight}:${scaleFlags}[v2];[v0_scaled]pad=${topVideo1Width}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v0];[v1_scaled]pad=${topVideo2Width}:${topRowHeight}:(ow-iw)/2:(oh-ih)/2:black[v1];[v0][v1]hstack=inputs=2[top];[top][v2]vstack=inputs=2[out]`;
        }
      }

      console.log('计算的最终分辨率:', finalResolution);
      console.log('FFmpeg滤镜:', filterComplex);

      // 创建FFmpeg命令
      const command = ffmpeg();

      // 添加输入视频（最多4个）
      const inputCount = Math.min(videoFiles.length, 4);
      for (let i = 0; i < inputCount; i++) {
        console.log(`添加输入视频 ${i}: ${videoFiles[i].path}, 时间戳: ${safeTimestamp}`);
        command.input(videoFiles[i].path)
          .seekInput(safeTimestamp);
      }

      // 根据输出文件扩展名确定格式和质量参数
      const outputExt = path.extname(outputPath).toLowerCase();
      let outputOptions;

      if (outputExt === '.png') {
        // PNG格式：无损压缩
        outputOptions = [
          '-map', '[out]',
          '-frames:v', '1',
          '-f', 'image2',
          '-pix_fmt', 'rgba',  // PNG支持透明度
          '-y' // 覆盖输出文件
        ];
      } else {
        // JPEG格式：高质量压缩
        outputOptions = [
          '-map', '[out]',
          '-frames:v', '1',
          '-q:v', '1',  // 使用最高质量 (1是最高质量，31是最低质量)
          '-pix_fmt', 'yuvj420p',  // 使用全范围YUV格式以保持更好的色彩
          '-f', 'image2',
          '-y' // 覆盖输出文件
        ];
      }

      command
        .complexFilter(filterComplex)
        .outputOptions(outputOptions)
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('多视频截图FFmpeg命令:', commandLine);
        })
        .on('end', () => {
          console.log(`多视频合成截图完成: ${outputPath}`);

          // 如果需要返回base64数据
          if (returnBase64) {
            try {
              const imageBuffer = fs.readFileSync(outputPath);
              const base64Data = imageBuffer.toString('base64');
              const mimeType = 'image/png';
              const base64String = `data:${mimeType};base64,${base64Data}`;

              // 删除临时文件
              if (fs.existsSync(outputPath)) {
                fs.unlinkSync(outputPath);
              }

              resolve({
                success: true,
                base64: base64String,
                timestamp: safeTimestamp,
                videoCount: inputCount,
                resolution: finalResolution,
                videoName: `多视频合成 (${inputCount}个视频)`
              });
            } catch (error) {
              console.error('读取多视频截图文件失败:', error);
              reject(new Error(`读取多视频截图文件失败: ${error.message}`));
            }
          } else {
            resolve({
              success: true,
              outputPath: outputPath,
              tempFilePath: tempFilePath, // 临时文件路径（如果有）
              timestamp: safeTimestamp,
              videoCount: inputCount,
              resolution: finalResolution,
              videoName: `多视频合成 (${inputCount}个视频)` // 添加视频名称
            });
          }
        })
        .on('error', (err) => {
          console.error(`多视频合成截图失败: ${err.message}`);
          console.error('FFmpeg stderr:', err.message);
          reject(new Error(`多视频合成截图失败: ${err.message}`));
        })
        .run();

    } catch (error) {
      console.error('多视频合成截图错误:', error);
      reject(error);
    }
  });
}

module.exports = {
  captureFrame,
  clipVideo,
  clipMultiVideo,
  captureMultiVideoFrame,
  getVideoInfo
};
