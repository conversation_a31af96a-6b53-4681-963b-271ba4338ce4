// 简化的 Toaster 组件，避免 Chakra UI v3 兼容性问题
import React from 'react';

// 简单的 toaster 实现
export const toaster = {
  success: (message: string) => {
    console.log('✅ Success:', message);
    // 可以在这里添加简单的通知逻辑
  },
  error: (message: string) => {
    console.error('❌ Error:', message);
    // 可以在这里添加简单的通知逻辑
  },
  info: (message: string) => {
    console.log('ℹ️ Info:', message);
    // 可以在这里添加简单的通知逻辑
  },
  dismiss: (id?: string) => {
    console.log('Dismiss toast:', id);
  }
};

// 简化的 Toaster 组件
export const Toaster = () => {
  // 暂时返回空组件，避免 Chakra UI v3 兼容性问题
  return null;
};
