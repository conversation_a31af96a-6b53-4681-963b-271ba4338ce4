{"name": "meea-viofo-all", "version": "25.07.18-1805", "description": "MEEA VIOFO - 专业的行车记录仪视频管理工具", "main": "electron/main.js", "homepage": "./", "author": "PerccyKing <<EMAIL>>", "license": "MIT", "engines": {"node": ">=20.19.0"}, "scripts": {"dev": "concurrently \"yarn dev:vite\" \"wait-on http://localhost:5174 && yarn dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "prebuild": "node scripts/generate-build-version.js && node scripts/inject-version.js", "build": "yarn prebuild && vite build", "preview": "vite preview", "electron": "electron .", "pack": "electron-builder --dir", "predist": "node scripts/generate-build-version.js && node scripts/inject-version.js", "build:windows-x64": "yarn predist && yarn build && electron-builder --win --x64 --config=electron-builder-win-x64.json --publish=never", "build:windows-x64-debug": "cross-env MEEA_WINDOWS_X64_DEBUG=true DEBUG_MODE=true yarn build:windows-x64", "build:windows-x64-arm64": "yarn predist && yarn build && electron-builder --win --x64 --arm64 --publish=never", "build:windows-arm64": "yarn predist && yarn build && electron-builder --win --arm64 --config=electron-builder-win-arm64.json --publish=never", "build:windows-arm64-debug": "cross-env MEEA_WINDOWS_ARM64_DEBUG=true DEBUG_MODE=true yarn build:windows-arm64", "build:macos-x64": "yarn predist && yarn build && electron-builder --mac --x64 --publish=never", "build:macos-arm64": "cross-env NODE_ENV=production yarn predist && yarn build && node scripts/clean-mac-arm64-build.js && electron-builder --mac --arm64 --config=electron-builder-mac-arm64.json --publish=never", "build:linux-x64": "yarn predist && yarn build && electron-builder --linux --x64 --publish=never", "build:linux-arm64": "yarn predist && yarn build && electron-builder --linux --arm64 --publish=never", "build:windows": "yarn predist && yarn build && electron-builder --win --x64 --arm64 --publish=never", "build:windows-debug": "cross-env MEEA_WINDOWS_DEBUG=true DEBUG_MODE=true yarn build:windows", "build:macos": "yarn predist && yarn build && electron-builder --mac --x64 --arm64 --publish=never", "build:linux": "yarn predist && yarn build && electron-builder --linux --x64 --arm64 --publish=never", "build:all": "yarn predist && yarn build && electron-builder --mac --win --linux --x64 --arm64 --publish=never", "verify:build": "node scripts/verify-build-config.js", "verify:output": "node scripts/verify-build-output.js", "diagnose:network": "node scripts/diagnose-network.js", "install:electron": "npm install electron@37.2.0 --save-dev", "cleanup": "./scripts/cleanup-space.sh", "setup:ffmpeg": "node scripts/setup-ffmpeg.js"}, "build": {"appId": "com.meea.viofo", "productName": "MEEA-VIOFO", "copyright": "Copyright © 2024 MEEA", "artifactName": "${productName}-${version}-${os}-${arch}.${ext}", "directories": {"output": "dist", "buildResources": "build"}, "files": ["dist/assets/**/*", "dist/index.html", "dist/*.svg", "dist/*.png", "electron/**/*", "keys/**/*", "package.json", "node_modules/fluent-ffmpeg/**/*", "node_modules/exiftool-vendored/**/*", "!node_modules/exiftool-vendored/bin/**/*", "!ffmpeg/**/*", "!dist/mac/**/*", "!dist/mac-arm64/**/*", "!dist/linux/**/*", "!dist/win-*/**/*", "!dist/*.dmg", "!dist/*.zip", "!dist/*.exe", "!dist/*.AppImage", "!dist/*.tar.gz", "!dist/*.deb", "!dist/*.rpm", "!dist/builder-*.yml", "!dist/builder-*.yaml"], "extraResources": [], "mac": {"icon": "build/icons/icon.icns", "category": "public.app-category.video", "hardenedRuntime": false, "gatekeeperAssess": false, "identity": null, "type": "distribution", "minimumSystemVersion": "10.15.0", "files": ["!ffmpeg/win-*/**/*", "!ffmpeg/linux-*/**/*"], "extraResources": [{"from": "ffmpeg/mac-x64", "to": "ffmpeg/mac-x64", "filter": ["**/*"]}, {"from": "ffmpeg/mac-arm64", "to": "ffmpeg/mac-arm64", "filter": ["**/*"]}], "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "extendInfo": {"NSCameraUsageDescription": "此应用需要访问摄像头以处理视频文件", "NSMicrophoneUsageDescription": "此应用需要访问麦克风以处理音频文件", "NSLocationUsageDescription": "此应用需要访问位置信息以显示GPS轨迹"}, "bundleVersion": "25.07.18-1805"}, "dmg": {"sign": false, "writeUpdateInfo": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"icon": "build/icons/icon.ico", "files": ["!ffmpeg/mac-*/**/*", "!ffmpeg/linux-*/**/*"], "extraResources": [{"from": "ffmpeg/win-x64", "to": "ffmpeg/win-x64", "filter": ["**/*"]}, {"from": "ffmpeg/win-arm64", "to": "ffmpeg/win-arm64", "filter": ["**/*"]}, {"from": "exiftool/win-x64", "to": "exiftool/win-x64", "filter": ["**/*"]}, {"from": "exiftool/win-arm64", "to": "exiftool/win-arm64", "filter": ["**/*"]}], "target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-Setup-${version}-${os}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MEEA-VIOFO", "artifactName": "${productName}-Setup-${version}-${os}-${arch}.${ext}", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Video", "include": "build/installer.nsh"}, "linux": {"icon": "build/icons/icon.png", "category": "AudioVideo", "synopsis": "专业的行车记录仪视频管理工具", "description": "MEEA VIOFO 是一款专业的行车记录仪视频管理工具，支持GPS轨迹显示、视频预览、缩略图生成等功能。", "files": ["!ffmpeg/mac-*/**/*", "!ffmpeg/win-*/**/*"], "extraResources": [{"from": "ffmpeg/linux-x64", "to": "ffmpeg/linux-x64", "filter": ["**/*"]}, {"from": "ffmpeg/linux-arm64", "to": "ffmpeg/linux-arm64", "filter": ["**/*"]}], "desktop": {"entry": {"Name": "MEEA-VIOFO", "Comment": "专业的行车记录仪视频管理工具", "Keywords": "video;dashcam;gps;media;player;", "StartupWMClass": "MEEA-VIOFO"}}, "target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "tar.gz", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}]}, "compression": "normal", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "publish": null, "generateUpdatesFilesForAllChannels": false, "detectUpdateChannel": false, "asar": true, "asarUnpack": ["node_modules/exiftool-vendored.pl/bin/**/*"], "afterPack": "scripts/after-pack.js", "buildVersion": "25.07.18-1805"}, "devDependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@types/node": "^24.0.12", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "electron-builder": "^26.0.12", "typescript": "^5.8.3", "vite": "^7.0.3", "wait-on": "^8.0.3"}, "optionalDependencies": {"dmg-license": "^1.0.11"}, "dependencies": {"@chakra-ui/react": "^3.22.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@rollup/rollup-darwin-arm64": "^4.46.2", "exiftool-vendored": "^30.3.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.23.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-is": "^18.3.1", "recharts": "^3.1.0"}}