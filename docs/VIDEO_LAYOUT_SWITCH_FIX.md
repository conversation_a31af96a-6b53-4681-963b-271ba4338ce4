# 视频布局切换修复

## 🎯 问题描述

当播放器有多个视频的时候，点击了主视频进入独占模式，然后从文件列表单独选择一个视频后，不能切换到单个视频播放的布局。

## 🔍 问题分析

### 根本原因：
1. **主视频独占状态未重置**：从文件列表选择单个视频时，`MultiVideoPlayer` 组件中的 `isMainVideoFocused` 状态没有被重置
2. **状态同步问题**：App.tsx 中的 `isMainVideoFocused` 状态被重置了，但 `MultiVideoPlayer` 内部的状态没有同步
3. **布局逻辑冲突**：单视频模式下仍然保持主视频独占模式的布局

### 问题流程：
```
1. 多视频播放 → 点击主视频 → 进入主视频独占模式 (isMainVideoFocused = true)
2. 从文件列表选择单个视频 → App.tsx 重置状态 → MultiVideoPlayer 内部状态未重置
3. 结果：单视频模式下仍然显示主视频独占布局
```

## ✅ 解决方案

### 1. **在 App.tsx 中重置主视频独占状态**

#### A. 修改 `handleFileSelect` 函数
```javascript
// 修复前：没有重置主视频独占状态
const handleFileSelect = useCallback(async (file: VideoFile, options?) => {
  setVideoFiles([file]);
  // ... 其他重置逻辑
  setTimeout(() => {
    multiVideoPlayerRef.current?.resetZoom?.();
  }, 100);
}, []);

// 修复后：重置主视频独占状态
const handleFileSelect = useCallback(async (file: VideoFile, options?) => {
  setVideoFiles([file]);
  // ... 其他重置逻辑
  
  // 重置主视频独占模式状态
  setIsMainVideoFocused(false);
  
  setTimeout(() => {
    multiVideoPlayerRef.current?.resetZoom?.();
    multiVideoPlayerRef.current?.returnToMultiVideoLayout?.(); // 调用重置方法
  }, 100);
}, []);
```

#### B. 修改 `handleVideoPlay` 函数
```javascript
// 修复前：没有重置主视频独占状态
const handleVideoPlay = useCallback(async (files: VideoFile[]) => {
  setVideoFiles(files);
  // ... 其他重置逻辑
  setTimeout(() => {
    multiVideoPlayerRef.current?.resetZoom?.();
  }, 100);
}, []);

// 修复后：重置主视频独占状态
const handleVideoPlay = useCallback(async (files: VideoFile[]) => {
  setVideoFiles(files);
  // ... 其他重置逻辑
  
  // 重置主视频独占模式状态
  setIsMainVideoFocused(false);
  
  setTimeout(() => {
    multiVideoPlayerRef.current?.resetZoom?.();
    multiVideoPlayerRef.current?.returnToMultiVideoLayout?.(); // 调用重置方法
  }, 100);
}, []);
```

### 2. **在 MultiVideoPlayer 中添加自动重置逻辑**

```javascript
// 当视频文件变化时重置主视频独占状态
useEffect(() => {
  // 如果从多视频切换到单视频，或者视频文件完全改变，重置主视频独占状态
  if (videoFiles.length <= 1 && isMainVideoFocused) {
    setIsMainVideoFocused(false);
    setFocusedVideoIndex(null);
    onMainVideoFocusChange?.(false);
  }
}, [videoFiles.length, isMainVideoFocused, onMainVideoFocusChange]);
```

## 📦 修复版本信息

- **修复时间**: 2025-08-03 07:00
- **修复内容**: 视频布局切换状态重置
- **影响范围**: App.tsx, MultiVideoPlayer.tsx

## 🎨 修复效果

### ✅ **正确的布局切换流程**
```
场景1：多视频 → 主视频独占 → 选择单视频
1. 多视频播放 (isMainVideoFocused = false)
2. 点击主视频 (isMainVideoFocused = true, 显示独占布局)
3. 选择单视频 (isMainVideoFocused = false, 显示单视频布局) ✅

场景2：多视频 → 主视频独占 → 选择多视频
1. 多视频播放 (isMainVideoFocused = false)
2. 点击主视频 (isMainVideoFocused = true, 显示独占布局)
3. 选择多视频 (isMainVideoFocused = false, 显示多视频布局) ✅
```

### ✅ **状态同步机制**
```
App.tsx 状态重置:
- setIsMainVideoFocused(false)
- 调用 multiVideoPlayerRef.current?.returnToMultiVideoLayout?.()

MultiVideoPlayer 内部重置:
- setIsMainVideoFocused(false)
- setFocusedVideoIndex(null)
- onMainVideoFocusChange?.(false)

自动检测重置:
- 监听 videoFiles.length 变化
- 单视频模式下自动重置独占状态
```

## 🧪 功能验证

### 测试步骤

#### 1. **多视频到单视频切换测试**
```
操作：
1. 加载多个视频文件
2. 点击主视频进入独占模式
3. 从文件列表选择单个视频

预期：
- 显示单视频播放布局
- 没有独占模式的覆盖层
- 视频控制正常
```

#### 2. **多视频到多视频切换测试**
```
操作：
1. 加载多个视频文件
2. 点击主视频进入独占模式
3. 从文件列表选择其他多个视频

预期：
- 显示多视频播放布局
- 退出独占模式
- 所有视频正常显示
```

#### 3. **状态一致性测试**
```
操作：
1. 在各种切换场景下检查状态
2. 观察布局是否正确

预期：
- App.tsx 和 MultiVideoPlayer 状态同步
- 布局与视频数量匹配
- 没有状态冲突
```

## 🎯 技术细节

### 状态管理层次
```
App.tsx (父组件):
- isMainVideoFocused: 全局状态
- 负责重置和同步

MultiVideoPlayer (子组件):
- isMainVideoFocused: 内部状态
- 负责布局渲染和用户交互
- 通过 onMainVideoFocusChange 同步到父组件
```

### 重置时机
```
1. 文件选择时 (handleFileSelect):
   - 切换到单视频模式
   - 立即重置独占状态

2. 多视频播放时 (handleVideoPlay):
   - 切换到多视频模式
   - 立即重置独占状态

3. 自动检测时 (useEffect):
   - 监听视频数量变化
   - 单视频模式下自动重置
```

### 调用顺序
```
1. App.tsx 重置全局状态
2. 调用 MultiVideoPlayer.returnToMultiVideoLayout()
3. MultiVideoPlayer 重置内部状态
4. 触发布局重新渲染
5. useEffect 自动检测确保状态一致
```

## 🏆 完整修复状态

现在视频布局切换功能完全正常：

1. ✅ **多视频到单视频切换** - 正确显示单视频布局
2. ✅ **多视频到多视频切换** - 正确显示多视频布局
3. ✅ **主视频独占状态重置** - 状态正确同步
4. ✅ **自动检测机制** - 防止状态不一致
5. ✅ **用户体验优化** - 布局切换流畅自然

---

**修复时间**: 2025-08-03 07:00  
**问题类型**: 视频布局切换状态管理  
**解决方案**: 多层次状态重置 + 自动检测机制  
**状态**: 视频布局切换功能完全修复
