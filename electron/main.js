const buildInfo = require('./build-info.json');
const { app, BrowserWindow, Menu, dialog, ipcMain, protocol, globalShortcut, shell, clipboard } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { spawn } = require('child_process');
const { ExifTool } = require('exiftool-vendored');
const { convertGpsTrack } = require('./gps-converter');
const { windowsExifToolFix } = require('./windows-exiftool-fix');
// 注意：FFmpeg无法提取VIOFO的GPS数据，所以直接使用ExifTool
const {
  generateThumbnails,
  generateThumbnail,
  preloadThumbnails,
  getVideoDuration,
  getCacheStats,
  cleanupCache
} = require('./thumbnail-generator');
const {
  captureFrame,
  clipVideo,
  clipMultiVideo,
  captureMultiVideoFrame,
  getVideoInfo
} = require('./clip-service');
const { machineIdGenerator } = require('./machine-id-generator');
const { LicenseService } = require('./license-service');
const { fixFFmpegPermissionsAtRuntime } = require('./ffmpeg-runtime-fix');
const { fixWindowsGPSIssues } = require('./windows-gps-fix');
const { windowsClipFix } = require('./windows-clip-fix');
const logger = require('./logger');

// 配置管理
const configPath = path.join(app.getPath('userData'), 'config.json');

// 许可证服务实例
let licenseService;

// 默认配置
const defaultConfig = {
  lastVideoFolder: null,
  windowBounds: {
    width: 1400,
    height: 900
  },
  amapApiKey: null // 高德地图App Key
};

// 读取配置
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      return { ...defaultConfig, ...config };
    }
  } catch (error) {
    if (isDev) {
      console.error('读取配置文件失败:', error);
    }
  }
  return defaultConfig;
}

// 保存配置
function saveConfig(config) {
  try {
    // 确保配置目录存在
    const configDir = path.dirname(configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    return true;
  } catch (error) {
    if (isDev) {
      console.error('保存配置文件失败:', error);
    }
    return false;
  }
}
const isDev = process.env.NODE_ENV === 'development';
const isPackaged = app.isPackaged;

// 多种方式检测调试模式
// 调试包默认开启调试模式
const isDebugBuild = app.getName && app.getName().includes('DEBUG');

// 🔧 可配置的强制调试模式（用于排查问题）
// 如果应用名称检测失败，可以通过以下方式强制启用：
const forceDebugMode = false; // 设置为 true 强制启用调试模式，正式版本应为 false

const isDebugMode = forceDebugMode || // 强制调试模式（可配置）
                   isDebugBuild || // 调试包默认开启
                   process.env.DEBUG_MODE === 'true' ||
                   process.env.MEEA_DEBUG === 'true' ||
                   process.env.MEEA_WINDOWS_ARM64_DEBUG === 'true' || // Windows ARM64 调试模式
                   process.env.MEEA_WINDOWS_X64_DEBUG === 'true' || // Windows x64 调试模式
                   process.env.MEEA_WINDOWS_DEBUG === 'true' || // 通用 Windows 调试模式
                   process.argv.includes('--debug') ||
                   process.argv.includes('--verbose');

// 开发者工具启用条件：开发模式 OR 调试模式
const devToolsEnabled = isDev || isDebugMode;

// 配置信息将在应用启动后输出

// 智能日志控制系统
// 开发环境：始终启用日志
// 生产环境 + DEBUG_MODE=true：启用日志（用于调试版本）
// 生产环境 + DEBUG_MODE=false/未设置：完全禁用日志（正式版本）
const shouldEnableLogging = isDev || isDebugMode;

// 调试信息输出（始终输出，用于诊断）
const diagnosticInfo = {
  NODE_ENV: process.env.NODE_ENV,
  DEBUG_MODE: process.env.DEBUG_MODE,
  MEEA_DEBUG: process.env.MEEA_DEBUG,
  isPackaged: isPackaged,
  isDev: isDev,
  isDebugBuild: isDebugBuild,
  forceDebugMode: forceDebugMode,
  isDebugMode: isDebugMode,
  shouldEnableLogging: shouldEnableLogging,
  appName: app.getName ? app.getName() : 'N/A',
  appPath: app.getAppPath ? app.getAppPath() : 'N/A',
  execPath: process.execPath,
  platform: process.platform,
  arch: process.arch,
  processArgv: process.argv.slice(-3)
};

console.log('🔍 [DIAGNOSTIC] 环境诊断信息:');
Object.entries(diagnosticInfo).forEach(([key, value]) => {
  console.log(`   ${key}:`, value);
});

// 记录到日志文件
logger.info('System', '环境诊断信息', diagnosticInfo);

// 创建增强的日志函数
function createEnhancedLogger(originalFn, level, emoji) {
  return (...args) => {
    // 控制台输出
    originalFn(`${emoji} [${level}]`, ...args);

    // 文件日志输出
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.log('App', level, message);
  };
}

if (!shouldEnableLogging && isPackaged) {
  const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug
  };

  // 在调试模式下不禁用控制台日志
  if (!isDebugMode) {
    // 延迟禁用日志，确保诊断信息能输出
    setTimeout(() => {
      console.log('🔇 [DIAGNOSTIC] 正在禁用控制台日志输出...');
      logger.info('System', '禁用控制台日志输出，但保持文件日志');

      // 禁用控制台输出，但保持文件日志
      console.log = (...args) => {
        const message = args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        logger.debug('App', message);
      };
      console.warn = (...args) => {
        const message = args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        logger.warn('App', message);
      };
      console.error = (...args) => {
        const message = args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        logger.error('App', message);
      };
      console.info = (...args) => {
        const message = args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        logger.info('App', message);
      };
      console.debug = (...args) => {
        const message = args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        logger.debug('App', message);
      };

      // 保留原始方法供紧急情况使用
      console._original = originalConsole;
    }, 2000); // 2秒后禁用控制台日志
  } else {
    // 调试模式：保持控制台日志启用
    console.log('🐛 [DEBUG] 调试模式：保持控制台日志启用');
    logger.info('System', '调试模式：保持控制台日志启用');
  }
} else if (isPackaged && isDebugMode) {
  // 调试模式下添加特殊标识和文件日志
  console.log('🐛 [DIAGNOSTIC] 启用调试模式日志...');
  logger.info('System', '启用调试模式日志');

  setTimeout(() => {
    console.log = createEnhancedLogger(console.log, 'DEBUG', '🐛');
    console.info = createEnhancedLogger(console.info, 'INFO', 'ℹ️');
    console.warn = createEnhancedLogger(console.warn, 'WARN', '⚠️');
    console.error = createEnhancedLogger(console.error, 'ERROR', '❌');

    console.log('调试日志系统已激活');
    console.log('日志文件位置:', logger.getCurrentLogFile());
  }, 1000); // 1秒后启用调试日志
} else {
  console.log('📝 [DIAGNOSTIC] 使用标准日志模式');
  logger.info('System', '使用标准日志模式');
}

// 移除协议注册，直接使用文件访问

// 简化的调试信息输出函数
function logDebugInfo() {
  console.log('🎉 调试模式已启用！');
  console.log('📋 应用信息:');
  console.log(`  - 版本: ${buildInfo.version}`);
  console.log(`  - 构建时间: ${buildInfo.buildTime}`);
  console.log(`  - 平台: ${process.platform} ${process.arch}`);
  console.log(`  - 调试模式: ${isDebugMode}`);
  console.log('');
  console.log('🛠️ Chrome 开发者工具已启用');
  console.log('💡 提示: 可按 F12 打开/关闭开发者工具');
  console.log('');
  console.log('=== 应用启动完成，开始记录运行日志 ===');
}

let mainWindow;
let logViewerWindow = null;
let imagePreviewWindow = null;

// 创建最小应用菜单的函数
function createMinimalAppMenu() {
  try {
    const template = [];

    if (process.platform === 'darwin') {
      // macOS: 只保留应用程序菜单
      template.push({
        label: app.getName(),
        submenu: [
          {
            label: '关于 ' + app.getName(),
            role: 'about'
          },
          { type: 'separator' },
          {
            label: '隐藏 ' + app.getName(),
            accelerator: 'Command+H',
            role: 'hide'
          },
          {
            label: '隐藏其他',
            accelerator: 'Command+Shift+H',
            role: 'hideothers'
          },
          {
            label: '显示全部',
            role: 'unhide'
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: 'Command+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      });
    } else {
      // Windows/Linux: 根据调试模式决定菜单内容
      if (isDebugMode) {
        // 调试模式：添加开发者工具菜单
        template.push({
          label: '调试',
          submenu: [
            {
              label: '开发者工具',
              accelerator: 'F12',
              click: (item, focusedWindow) => {
                if (focusedWindow) {
                  focusedWindow.webContents.toggleDevTools();
                }
              }
            },
            {
              label: '重新加载',
              accelerator: 'Ctrl+R',
              click: (item, focusedWindow) => {
                if (focusedWindow) {
                  focusedWindow.reload();
                }
              }
            },
            {
              label: '强制重新加载',
              accelerator: 'Ctrl+Shift+R',
              click: (item, focusedWindow) => {
                if (focusedWindow) {
                  focusedWindow.webContents.reloadIgnoringCache();
                }
              }
            }
          ]
        });
      }
      // 如果不是调试模式，保持空菜单（只有标题栏）
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

    console.log('✅ 已创建最小应用菜单');
  } catch (error) {
    console.error('❌ 创建最小应用菜单失败:', error);
  }
}

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    icon: path.join(__dirname, '../assets/logo.svg'), // 设置应用图标
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // 允许访问本地文件
      allowRunningInsecureContent: true, // 允许不安全内容
      experimentalFeatures: true, // 启用实验性功能
      devTools: devToolsEnabled // 在开发模式或调试模式下启用开发者工具
    },
    show: false, // 先不显示窗口，等菜单栏设置完成后再显示
    titleBarStyle: 'default', // 保留原生标题栏
    center: true, // 居中显示
    resizable: true,
    minimizable: true,
    maximizable: true,
    autoHideMenuBar: false, // 不自动隐藏菜单栏，保留空白标题栏
    skipTaskbar: false // 确保在任务栏中显示
  });

  // 禁用 CSP 以允许地图正常工作
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };
    // 删除 CSP 头部
    delete responseHeaders['Content-Security-Policy'];
    delete responseHeaders['content-security-policy'];

    callback({ responseHeaders });
  });

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:5174');
    // 开发模式下打开开发者工具
    mainWindow.webContents.openDevTools();

    // 开发模式下添加开发者工具快捷键（仅在调试模式下启用）
    if (isDebugMode) {
      globalShortcut.register('CommandOrControl+Shift+I', () => {
        if (mainWindow) {
          mainWindow.webContents.toggleDevTools();
        }
      });

      // 添加刷新快捷键（仅在调试模式下启用）
      globalShortcut.register('CommandOrControl+R', () => {
        if (mainWindow) {
          mainWindow.reload();
        }
      });

      console.log('🐛 开发模式调试快捷键已启用: Ctrl+Shift+I (开发者工具), Ctrl+R (刷新)');
    } else {
      console.log('⚠️ 开发模式但非调试模式，跳过刷新和开发者工具快捷键');
    }

    // 应用退出时注销所有快捷键
    app.on('will-quit', () => {
      globalShortcut.unregisterAll();
    });
  } else {
    // 生产模式：加载构建后的文件
    logger.info('PROD', '加载生产模式文件...');
    logger.info('PROD', `__dirname: ${__dirname}`);
    logger.info('PROD', `process.cwd(): ${process.cwd()}`);
    logger.info('PROD', `app.getAppPath(): ${app.getAppPath()}`);

    const indexPath = path.join(__dirname, '../dist/index.html');
    logger.info('PROD', `计算的文件路径: ${indexPath}`);
    logger.info('PROD', `文件是否存在: ${require('fs').existsSync(indexPath)}`);

    mainWindow.loadFile(indexPath);

    // 如果启用了调试模式，在文件加载完成后打开开发者工具
    if (isDebugMode) {
      console.log('🐛 [DEBUG] 生产环境启用开发者工具 (调试模式)');

      // 等待页面加载完成
      mainWindow.webContents.once('did-finish-load', () => {
        logger.info('DEBUG', '页面加载完成，尝试打开开发者工具...');

        // 立即尝试打开开发者工具
        try {
          mainWindow.webContents.openDevTools();
          logger.info('DEBUG', 'openDevTools() 调用成功');
          logDebugInfo();
        } catch (error) {
          logger.error('DEBUG', `openDevTools() 调用失败: ${error.message}`);
        }

        // 验证是否成功打开
        setTimeout(() => {
          if (mainWindow.webContents.isDevToolsOpened()) {
            logger.info('DEBUG', '开发者工具已成功打开');
          } else {
            logger.warn('DEBUG', '开发者工具未打开，尝试分离模式...');
            try {
              mainWindow.webContents.openDevTools({ mode: 'detach' });
              logger.info('DEBUG', '分离模式 openDevTools() 调用成功');
            } catch (error) {
              logger.error('DEBUG', `分离模式 openDevTools() 调用失败: ${error.message}`);
            }
          }
        }, 1000);
      });

      // 页面加载失败的处理
      mainWindow.webContents.once('did-fail-load', (event, errorCode, errorDescription) => {
        logger.error('DEBUG', `页面加载失败: ${errorCode} ${errorDescription}`);
      });

      // 注册开发者工具快捷键（生产环境调试模式）
      try {
        // 方法1: 全局快捷键
        const f12Success = globalShortcut.register('F12', () => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.toggleDevTools();
            logger.info('DEBUG', 'F12 全局快捷键触发，切换开发者工具');
          }
        });

        const ctrlShiftISuccess = globalShortcut.register('CommandOrControl+Shift+I', () => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.toggleDevTools();
            logger.info('DEBUG', 'Ctrl+Shift+I 全局快捷键触发，切换开发者工具');
          }
        });

        const ctrlShiftJSuccess = globalShortcut.register('CommandOrControl+Shift+J', () => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.openDevTools();
            logger.info('DEBUG', 'Ctrl+Shift+J 全局快捷键触发，打开开发者工具');
          }
        });

        logger.info('DEBUG', '全局快捷键注册结果:');
        logger.info('DEBUG', `  - F12: ${f12Success ? '✅' : '❌'}`);
        logger.info('DEBUG', `  - Ctrl+Shift+I: ${ctrlShiftISuccess ? '✅' : '❌'}`);
        logger.info('DEBUG', `  - Ctrl+Shift+J: ${ctrlShiftJSuccess ? '✅' : '❌'}`);

        // 方法2: 窗口级别的键盘事件监听（备用方案）
        mainWindow.webContents.on('before-input-event', (event, input) => {
          // F12 键
          if (input.key === 'F12' && input.type === 'keyDown') {
            mainWindow.webContents.toggleDevTools();
            logger.info('DEBUG', 'F12 窗口事件触发，切换开发者工具');
          }

          // Ctrl+Shift+I
          if (input.control && input.shift && input.key === 'I' && input.type === 'keyDown') {
            mainWindow.webContents.toggleDevTools();
            logger.info('DEBUG', 'Ctrl+Shift+I 窗口事件触发，切换开发者工具');
          }

          // Ctrl+Shift+J
          if (input.control && input.shift && input.key === 'J' && input.type === 'keyDown') {
            mainWindow.webContents.openDevTools();
            logger.info('DEBUG', 'Ctrl+Shift+J 窗口事件触发，打开开发者工具');
          }
        });

        logger.info('DEBUG', '窗口级别键盘事件监听已启用');

      } catch (error) {
        console.error('❌ 注册开发者工具快捷键失败:', error);
      }

      // 显示调试模式确认消息
      setTimeout(() => {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'MEEA-VIOFO Debug Mode',
          message: '🐛 调试模式已启用',
          detail: `版本: ${buildInfo.version}\n构建时间: ${buildInfo.buildTime}\n平台: ${process.platform} ${process.arch}\n\n调试功能:\n✅ Chrome 开发者工具已启用\n✅ 详细日志记录已启用\n✅ 可按 F12 重新打开开发者工具\n\nChrome 开发者工具应该已经自动打开。\n如果没有看到，请按 F12 手动打开。`,
          buttons: ['确定', '重新打开开发者工具'],
          defaultId: 0
        }).then((result) => {
          if (result.response === 1) {
            mainWindow.webContents.openDevTools();
          }
        });
      }, 4000);
    } else {
      // 生产模式下不自动打开开发者工具，但保留快捷键访问
      console.log('🚀 生产模式：应用已启动，可按 F12 打开开发者工具');
    }

    // 应用退出时注销所有快捷键
    app.on('will-quit', () => {
      globalShortcut.unregisterAll();
      console.log('🔧 [DEBUG] 已注销所有全局快捷键');
    });
    // 允许调试快捷键，便于构建版本的调试和问题排查
    mainWindow.webContents.on('before-input-event', (event, input) => {
      console.log('🔍 检测到快捷键:', input.key, 'control:', input.control, 'meta:', input.meta, 'shift:', input.shift);

      // 不再禁用调试快捷键，允许在构建版本中使用console和开发者工具
      // 这有助于用户和开发者进行问题排查
      /*
      if (input.key === 'F12' ||
          (input.control && input.shift && input.key === 'I') ||
          (input.meta && input.alt && input.key === 'I')) {
        event.preventDefault();
        console.log('🚫 已阻止调试快捷键:', input.key);
      }
      */

      // 暂时不禁用刷新和其他快捷键，看看是否影响粘贴
      /*
      // 禁用刷新快捷键
      (input.control && input.key === 'R') ||
      (input.meta && input.key === 'R') ||
      input.key === 'F5' ||
      // 禁用其他调试快捷键
      (input.control && input.shift && (input.key === 'J' || input.key === 'C' || input.key === 'D')) ||
      (input.meta && input.shift && input.key === 'D') ||
      // 禁用控制台快捷键
      (input.control && input.shift && input.key === 'K') ||
      (input.meta && input.shift && input.key === 'K')
      */
    });

    // 允许右键菜单，便于访问开发者工具和调试功能
    mainWindow.webContents.on('context-menu', (event, params) => {
      // 允许所有右键菜单，包括"检查元素"等调试功能
      // 这有助于用户和开发者进行问题排查
      console.log('🖱️ 右键菜单:', params.inputFieldType);
      // 不再阻止右键菜单
      // event.preventDefault();
    });
  }

  // 为主窗口创建最小应用菜单
  createMinimalAppMenu();
  console.log('📋 主窗口已设置最小应用菜单');

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    // 在显示前再次确保最小应用菜单
    createMinimalAppMenu();
    mainWindow.show();
    console.log('👁️ 主窗口已显示，标题栏保留，功能菜单已隐藏');

    // 添加调试信息
    console.log('🔧 窗口调试信息:', {
      url: mainWindow.webContents.getURL(),
      title: mainWindow.getTitle(),
      isLoading: mainWindow.webContents.isLoading(),
      isLoadingMainFrame: mainWindow.webContents.isLoadingMainFrame()
    });

    // 延迟检查页面内容
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('🔧 页面调试信息:', {
          title: document.title,
          readyState: document.readyState,
          rootElement: document.getElementById('root') ? 'exists' : 'missing',
          bodyChildren: document.body.children.length,
          scripts: document.scripts.length,
          errors: window.onerror ? 'handler exists' : 'no handler'
        });

        // 检查是否有JavaScript错误
        window.addEventListener('error', (e) => {
          console.error('🚨 JavaScript错误:', e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
          console.error('🚨 未处理的Promise拒绝:', e.reason);
        });

        return 'Debug info logged';
      `).then(result => {
        console.log('✅ 页面调试脚本执行完成:', result);
      }).catch(error => {
        console.error('❌ 页面调试脚本执行失败:', error);
      });
    }, 1000);
  });

  // 当窗口被关闭时
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 创建日志查看器窗口（仅调试模式）
function createLogViewerWindow() {
  console.log('🔍 [DEBUG] createLogViewerWindow 被调用');
  console.log('🔍 [DEBUG] 当前调试状态:', {
    isDebugMode,
    isDebugBuild,
    appName: app.getName ? app.getName() : 'N/A',
    platform: process.platform
  });

  if (!isDebugMode) {
    console.log('⚠️ 日志查看器仅在调试模式下可用');
    console.log('⚠️ 调试模式检测失败，请检查应用名称是否包含 DEBUG');

    // 显示错误对话框帮助调试
    dialog.showErrorBox('调试功能不可用',
      `日志查看器仅在调试模式下可用。\n\n` +
      `当前状态:\n` +
      `- 应用名称: ${app.getName ? app.getName() : 'N/A'}\n` +
      `- 调试模式: ${isDebugMode}\n` +
      `- 调试构建: ${isDebugBuild}\n` +
      `- 平台: ${process.platform}\n\n` +
      `请确认使用的是调试版本 (MEEA-VIOFO-DEBUG)`
    );
    return;
  }

  if (logViewerWindow) {
    logViewerWindow.focus();
    return;
  }

  logViewerWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    title: 'MEEA-VIOFO 运行日志查看器',
    icon: path.join(__dirname, '../assets/logo.svg'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      devTools: devToolsEnabled
    },
    show: false,
    titleBarStyle: 'default',
    resizable: true,
    minimizable: true,
    maximizable: true,
    parent: mainWindow, // 设置为主窗口的子窗口
    modal: false,
    autoHideMenuBar: false // 保留标题栏
  });

  // 加载日志查看器页面
  const logViewerHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>运行日志查看器</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        .header {
            background-color: #2d2d30;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-bottom: 1px solid #3e3e42;
        }
        .header h1 {
            margin: 0;
            color: #ffffff;
            font-size: 18px;
        }
        .controls {
            margin-bottom: 15px;
        }
        .btn {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 8px 16px;
            margin-right: 10px;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
        }
        .btn:hover {
            background-color: #1177bb;
        }
        .btn.secondary {
            background-color: #5a5a5a;
        }
        .btn.secondary:hover {
            background-color: #6a6a6a;
        }
        #logContent {
            background-color: #0d1117;
            border: 1px solid #30363d;
            padding: 15px;
            height: 500px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .log-entry {
            margin-bottom: 2px;
        }
        .log-debug { color: #8b949e; }
        .log-info { color: #58a6ff; }
        .log-warn { color: #f85149; }
        .log-error { color: #ff6b6b; }
        .status {
            margin-top: 10px;
            font-size: 11px;
            color: #8b949e;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐛 MEEA-VIOFO 运行日志查看器</h1>
    </div>

    <div class="controls">
        <button class="btn" onclick="refreshLogs()">🔄 刷新日志</button>
        <button class="btn secondary" onclick="clearDisplay()">🗑️ 清空显示</button>
        <button class="btn secondary" onclick="openLogFolder()">📁 打开日志文件夹</button>
        <button class="btn secondary" onclick="exportLogs()">💾 导出日志</button>
    </div>

    <div id="logContent">正在加载日志...</div>
    <div class="status" id="status">准备就绪</div>

    <script>
        let autoRefresh = true;
        let refreshInterval;

        // 刷新日志内容
        async function refreshLogs() {
            try {
                document.getElementById('status').textContent = '正在加载日志...';
                const logs = await window.electronAPI.getLogContent();
                document.getElementById('logContent').innerHTML = formatLogs(logs);
                document.getElementById('status').textContent = \`最后更新: \${new Date().toLocaleTimeString()}\`;

                // 自动滚动到底部
                const logContent = document.getElementById('logContent');
                logContent.scrollTop = logContent.scrollHeight;
            } catch (error) {
                document.getElementById('logContent').innerHTML = \`<div class="log-error">加载日志失败: \${error.message}</div>\`;
                document.getElementById('status').textContent = '加载失败';
            }
        }

        // 格式化日志内容
        function formatLogs(logs) {
            if (!logs || logs.length === 0) {
                return '<div class="log-info">暂无日志内容</div>';
            }

            return logs.split('\\n').map(line => {
                if (!line.trim()) return '';

                let className = 'log-entry';
                if (line.includes('[ERROR]')) className += ' log-error';
                else if (line.includes('[WARN]')) className += ' log-warn';
                else if (line.includes('[INFO]')) className += ' log-info';
                else if (line.includes('[DEBUG]')) className += ' log-debug';

                return \`<div class="\${className}">\${escapeHtml(line)}</div>\`;
            }).join('');
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 清空显示
        function clearDisplay() {
            document.getElementById('logContent').innerHTML = '<div class="log-info">显示已清空</div>';
            document.getElementById('status').textContent = '显示已清空';
        }

        // 打开日志文件夹
        async function openLogFolder() {
            try {
                await window.electronAPI.openLogFolder();
            } catch (error) {
                alert('打开日志文件夹失败: ' + error.message);
            }
        }

        // 导出日志
        async function exportLogs() {
            try {
                const result = await window.electronAPI.exportLogs();
                if (result.success) {
                    alert('日志已导出到: ' + result.path);
                } else {
                    alert('导出失败: ' + result.error);
                }
            } catch (error) {
                alert('导出日志失败: ' + error.message);
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            refreshLogs();

            // 设置自动刷新
            refreshInterval = setInterval(() => {
                if (autoRefresh) {
                    refreshLogs();
                }
            }, 3000); // 每3秒刷新一次
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>`;

  // 创建临时HTML文件
  const tempHtmlPath = path.join(app.getPath('temp'), 'meea-log-viewer.html');
  fs.writeFileSync(tempHtmlPath, logViewerHtml, 'utf8');

  logViewerWindow.loadFile(tempHtmlPath);

  // 日志查看器窗口使用最小应用菜单
  // 不需要特殊设置，继承应用菜单

  logViewerWindow.once('ready-to-show', () => {
    logViewerWindow.show();
    console.log('📊 日志查看器窗口已打开');
  });

  logViewerWindow.on('closed', () => {
    logViewerWindow = null;
    // 清理临时文件
    try {
      if (fs.existsSync(tempHtmlPath)) {
        fs.unlinkSync(tempHtmlPath);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  });
}

// 创建图片预览JavaScript脚本
function createImagePreviewScript() {
  return `
        let scale = 1;
        let position = { x: 0, y: 0 };
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let lastTouchDistance = 0;
        let initialTouchDistance = 0;
        let initialScale = 1;

        const imageContainer = document.getElementById('imageContainer');
        const imageWrapper = document.getElementById('imageWrapper');
        const previewImage = document.getElementById('previewImage');
        const zoomInfo = document.getElementById('zoomInfo');
        const zoomInBtn = document.getElementById('zoomIn');
        const zoomOutBtn = document.getElementById('zoomOut');
        const resetZoomBtn = document.getElementById('resetZoom');
        const fitWindowBtn = document.getElementById('fitWindow');

        function updateTransform() {
            imageWrapper.style.transform = \`translate(\${position.x}px, \${position.y}px) scale(\${scale})\`;
            zoomInfo.textContent = Math.round(scale * 100) + '%';

            zoomInBtn.disabled = scale >= 5;
            zoomOutBtn.disabled = scale <= 0.1;

            imageContainer.style.cursor = scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default';
        }

        function resetZoom() {
            scale = 1;
            position = { x: 0, y: 0 };
            updateTransform();
        }

        function fitToWindow() {
            const containerRect = imageContainer.getBoundingClientRect();
            const imageRect = previewImage.getBoundingClientRect();

            if (imageRect.width === 0 || imageRect.height === 0) return;

            const scaleX = containerRect.width / previewImage.naturalWidth;
            const scaleY = containerRect.height / previewImage.naturalHeight;
            scale = Math.min(scaleX, scaleY, 1);
            position = { x: 0, y: 0 };
            updateTransform();
        }

        function getTouchDistance(touches) {
            if (touches.length < 2) return 0;
            const touch1 = touches[0];
            const touch2 = touches[1];
            return Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
        }

        // 鼠标滚轮缩放
        imageContainer.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -0.1 : 0.1;
            scale = Math.max(0.1, Math.min(5, scale + delta));
            updateTransform();
        });

        // 鼠标拖拽
        imageContainer.addEventListener('mousedown', (e) => {
            if (scale > 1) {
                isDragging = true;
                imageWrapper.classList.add('no-transition');
                dragStart = {
                    x: e.clientX - position.x,
                    y: e.clientY - position.y
                };
                updateTransform();
            }
        });

        imageContainer.addEventListener('mousemove', (e) => {
            if (isDragging && scale > 1) {
                position = {
                    x: e.clientX - dragStart.x,
                    y: e.clientY - dragStart.y
                };
                updateTransform();
            }
        });

        imageContainer.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                imageWrapper.classList.remove('no-transition');
                updateTransform();
            }
        });

        imageContainer.addEventListener('mouseleave', () => {
            if (isDragging) {
                isDragging = false;
                imageWrapper.classList.remove('no-transition');
                updateTransform();
            }
        });

        // 触摸手势
        imageContainer.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                if (scale > 1) {
                    isDragging = true;
                    imageWrapper.classList.add('no-transition');
                    dragStart = {
                        x: e.touches[0].clientX - position.x,
                        y: e.touches[0].clientY - position.y
                    };
                    updateTransform();
                }
            } else if (e.touches.length === 2) {
                e.preventDefault();
                const distance = getTouchDistance(e.touches);
                initialTouchDistance = distance;
                lastTouchDistance = distance;
                initialScale = scale;
                isDragging = false;
                imageWrapper.classList.add('no-transition');
            }
        });

        imageContainer.addEventListener('touchmove', (e) => {
            if (e.touches.length === 1 && isDragging && scale > 1) {
                e.preventDefault();
                position = {
                    x: e.touches[0].clientX - dragStart.x,
                    y: e.touches[0].clientY - dragStart.y
                };
                updateTransform();
            } else if (e.touches.length === 2) {
                e.preventDefault();
                const distance = getTouchDistance(e.touches);
                if (initialTouchDistance > 0) {
                    const scaleChange = distance / initialTouchDistance;
                    scale = Math.max(0.1, Math.min(5, initialScale * scaleChange));
                    updateTransform();
                }
                lastTouchDistance = distance;
            }
        });

        imageContainer.addEventListener('touchend', (e) => {
            if (e.touches.length === 0) {
                isDragging = false;
                initialTouchDistance = 0;
                lastTouchDistance = 0;
                imageWrapper.classList.remove('no-transition');
                updateTransform();
            } else if (e.touches.length === 1) {
                initialTouchDistance = 0;
                lastTouchDistance = 0;
                if (scale > 1) {
                    isDragging = true;
                    dragStart = {
                        x: e.touches[0].clientX - position.x,
                        y: e.touches[0].clientY - position.y
                    };
                }
            }
        });

        // 按钮事件
        zoomInBtn.addEventListener('click', () => {
            scale = Math.min(5, scale + 0.2);
            updateTransform();
        });

        zoomOutBtn.addEventListener('click', () => {
            scale = Math.max(0.1, scale - 0.2);
            updateTransform();
        });

        resetZoomBtn.addEventListener('click', resetZoom);
        fitWindowBtn.addEventListener('click', fitToWindow);

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Escape':
                    window.close();
                    break;
                case '=':
                case '+':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        scale = Math.min(5, scale + 0.2);
                        updateTransform();
                    }
                    break;
                case '-':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        scale = Math.max(0.1, scale - 0.2);
                        updateTransform();
                    }
                    break;
                case '0':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        resetZoom();
                    }
                    break;
            }
        });

        // 图片加载完成后适应窗口
        previewImage.addEventListener('load', () => {
            fitToWindow();
        });

        // 窗口大小改变时重新适应
        window.addEventListener('resize', () => {
            if (scale === 1) {
                fitToWindow();
            }
        });

        // 初始化
        updateTransform();`;
}

// 创建图片预览HTML内容
function createImagePreviewHTML(imagePath, imageInfo = {}) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            user-select: none;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            backdrop-filter: blur(10px);
        }

        .toolbar button {
            background: transparent;
            border: none;
            color: #fff;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            font-size: 16px;
            transition: background-color 0.2s;
        }

        .toolbar button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .toolbar button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .zoom-info {
            color: #fff;
            font-size: 14px;
            min-width: 60px;
            text-align: center;
        }

        .image-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            cursor: grab;
        }

        .image-container.dragging {
            cursor: grabbing;
        }

        .image-wrapper {
            transition: transform 0.2s ease-out;
            transform-origin: center;
        }

        .image-wrapper.no-transition {
            transition: none;
        }

        .preview-image {
            max-width: none;
            max-height: none;
            pointer-events: none;
            draggable: false;
        }

        .info-panel {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .info-item {
            margin-bottom: 4px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <button id="zoomOut" title="缩小">−</button>
            <div class="zoom-info" id="zoomInfo">100%</div>
            <button id="zoomIn" title="放大">+</button>
            <button id="resetZoom" title="重置">⟲</button>
            <button id="fitWindow" title="适应窗口">⊞</button>
        </div>

        <div class="image-container" id="imageContainer">
            <div class="image-wrapper" id="imageWrapper">
                <img class="preview-image" id="previewImage" src="file://${imagePath}" alt="预览图片">
            </div>
        </div>

        <div class="info-panel">
            <div class="info-item"><strong>文件:</strong> ${path.basename(imagePath)}</div>
            ${imageInfo.width && imageInfo.height ? `<div class="info-item"><strong>尺寸:</strong> ${imageInfo.width} × ${imageInfo.height}</div>` : ''}
            ${imageInfo.videoName ? `<div class="info-item"><strong>视频:</strong> ${imageInfo.videoName}</div>` : ''}
            ${imageInfo.timestamp !== undefined ? `<div class="info-item"><strong>时间点:</strong> ${Math.floor(imageInfo.timestamp / 60)}:${String(Math.floor(imageInfo.timestamp % 60)).padStart(2, '0')}</div>` : ''}
        </div>
    </div>

    <script>
        ${createImagePreviewScript()}
    </script>
</body>
</html>`;
}

// 创建图片预览窗口
function createImagePreviewWindow(imagePath, imageInfo = {}) {
  console.log('🖼️ 创建图片预览窗口:', imagePath);

  // 如果已经有预览窗口，先关闭它
  if (imagePreviewWindow) {
    imagePreviewWindow.close();
    imagePreviewWindow = null;
  }

  // 获取主屏幕尺寸
  const { screen } = require('electron');
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // 计算窗口尺寸（占屏幕的80%，但不超过图片原始尺寸）
  const maxWidth = Math.floor(screenWidth * 0.8);
  const maxHeight = Math.floor(screenHeight * 0.8);

  let windowWidth = maxWidth;
  let windowHeight = maxHeight;

  // 如果有图片尺寸信息，按比例调整窗口大小
  if (imageInfo.width && imageInfo.height) {
    const imageAspectRatio = imageInfo.width / imageInfo.height;
    const maxAspectRatio = maxWidth / maxHeight;

    if (imageAspectRatio > maxAspectRatio) {
      // 图片更宽，以宽度为准
      windowWidth = Math.min(maxWidth, imageInfo.width);
      windowHeight = Math.floor(windowWidth / imageAspectRatio);
    } else {
      // 图片更高，以高度为准
      windowHeight = Math.min(maxHeight, imageInfo.height);
      windowWidth = Math.floor(windowHeight * imageAspectRatio);
    }
  }

  // 确保最小尺寸
  windowWidth = Math.max(800, windowWidth);
  windowHeight = Math.max(600, windowHeight);

  imagePreviewWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    title: `图片预览 - ${path.basename(imagePath)}`,
    icon: path.join(__dirname, '../assets/logo.svg'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // 允许访问本地文件
      devTools: devToolsEnabled
    },
    show: false,
    titleBarStyle: 'default',
    resizable: true,
    minimizable: true,
    maximizable: true,
    center: true,
    backgroundColor: '#000000', // 黑色背景
    autoHideMenuBar: false // 保留标题栏
  });

  // 创建预览页面的HTML内容
  const previewHTML = createImagePreviewHTML(imagePath, imageInfo);

  // 创建临时HTML文件
  const tempDir = path.join(require('os').tmpdir(), 'meea-image-preview');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  const tempHtmlPath = path.join(tempDir, `preview-${Date.now()}.html`);
  fs.writeFileSync(tempHtmlPath, previewHTML, 'utf8');

  // 加载预览页面
  imagePreviewWindow.loadFile(tempHtmlPath);

  // 图片预览窗口使用最小应用菜单
  // 不需要特殊设置，继承应用菜单

  // 窗口准备好后显示
  imagePreviewWindow.once('ready-to-show', () => {
    imagePreviewWindow.show();
    imagePreviewWindow.focus();
  });

  // 窗口关闭时清理
  imagePreviewWindow.on('closed', () => {
    imagePreviewWindow = null;
    // 清理临时HTML文件
    try {
      if (fs.existsSync(tempHtmlPath)) {
        fs.unlinkSync(tempHtmlPath);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  });

  return imagePreviewWindow;
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  // 输出配置信息用于调试
  console.log('🔧 [CONFIG] 应用配置信息:');
  console.log(`  - isDev: ${isDev}`);
  console.log(`  - isDebugMode: ${isDebugMode}`);
  console.log(`  - devToolsEnabled: ${devToolsEnabled}`);
  console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`  - isPackaged: ${isPackaged}`);

  logger.info('CONFIG', '应用配置信息:');
  logger.info('CONFIG', `  - isDev: ${isDev}`);
  logger.info('CONFIG', `  - isDebugMode: ${isDebugMode}`);
  logger.info('CONFIG', `  - devToolsEnabled: ${devToolsEnabled}`);
  logger.info('CONFIG', `  - NODE_ENV: ${process.env.NODE_ENV}`);
  logger.info('CONFIG', `  - isPackaged: ${isPackaged}`);

  // 显示构建信息
  const appInfo = {
    version: buildInfo.version,
    buildTime: buildInfo.buildDateTime,
    gitHash: buildInfo.gitHash,
    gitBranch: buildInfo.gitBranch,
    platform: buildInfo.platform,
    arch: buildInfo.arch
  };

  console.log('🚀 应用启动信息:');
  Object.entries(appInfo).forEach(([key, value]) => {
    console.log(`   ${key}:`, value);
  });

  logger.info('System', '应用启动', appInfo);

  // 在应用启动时创建最小应用菜单
  createMinimalAppMenu();
  console.log('📋 已在应用启动时设置最小应用菜单');

  try {
    // 修复FFmpeg权限（仅在打包环境下）
    fixFFmpegPermissionsAtRuntime();

    // Windows平台特定修复
    fixWindowsGPSIssues();

    // Windows截图剪辑修复
    if (process.platform === 'win32') {
      try {
        windowsClipFix.initialize();
        console.log('✅ Windows截图剪辑修复初始化成功');

        // 启动时检查FFmpeg可用性
        if (windowsClipFix.isAvailable()) {
          console.log('🎬 FFmpeg在Windows上可用，截图剪辑功能正常');
        } else {
          console.warn('⚠️ FFmpeg在Windows上不可用，截图剪辑功能可能受限');
        }
      } catch (error) {
        console.error('❌ Windows截图剪辑修复初始化失败:', error.message);

        // 检查是否是EFTYPE错误
        if (error.message.includes('EFTYPE')) {
          console.error('🚨 检测到EFTYPE错误 - 这通常表示:');
          console.error('   1. FFmpeg文件损坏或不完整');
          console.error('   2. 缺少Microsoft Visual C++ Redistributable');
          console.error('   3. 防病毒软件阻止执行');
          console.error('   4. 文件权限不足');
          console.error('💡 建议用户:');
          console.error('   1. 重新安装应用程序');
          console.error('   2. 安装Visual C++ Redistributable');
          console.error('   3. 将应用添加到防病毒软件白名单');
          console.error('   4. 以管理员权限运行应用');
        }
      }
    }

    // 初始化许可证服务
    if (isDev) {
      console.log('正在初始化许可证服务...');
    }
    licenseService = new LicenseService();
    if (isDev) {
      console.log('许可证服务初始化成功');
    }
  } catch (error) {
    if (isDev) {
      console.error('许可证服务初始化失败:', error);
    }
    // 即使许可证服务初始化失败，也要继续启动应用
    licenseService = null;
  }

  createWindow();
});

// 清理临时截图文件
function cleanupTempScreenshots() {
  try {
    const tempDirs = [
      path.join(app.getPath('temp'), 'meea-screenshots'),
      path.join(require('os').tmpdir(), 'meea-screenshots'),
      path.join(require('os').tmpdir(), 'meea-image-preview')
    ];

    let cleanedCount = 0;
    tempDirs.forEach(tempDir => {
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        files.forEach(file => {
          try {
            const filePath = path.join(tempDir, file);
            fs.unlinkSync(filePath);
            cleanedCount++;
          } catch (error) {
            // 忽略删除错误
          }
        });

        // 尝试删除空目录
        try {
          fs.rmdirSync(tempDir);
        } catch (error) {
          // 忽略删除目录错误
        }
      }
    });

    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个临时截图文件`);
    }
  } catch (error) {
    console.error('清理临时截图文件失败:', error);
  }
}

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', async () => {
  // 清理ExifTool实例
  if (exifTool) {
    try {
      await exifTool.end();
      exifTool = null;
    } catch (error) {
      if (isDev) {
        console.error('清理ExifTool时出错:', error);
      }
    }
  }

  // 清理Windows ExifTool包装器
  if (windowsExifToolWrapper) {
    windowsExifToolWrapper = null;
  }

  // 清理临时截图文件
  cleanupTempScreenshots();

  // 注销所有全局快捷键
  globalShortcut.unregisterAll();

  // 在开发模式下，保持应用运行以便调试
  if (isDev) {
    console.log('开发模式：应用将保持运行状态');
    return;
  }

  // 在 macOS 上，应用和菜单栏通常会保持活动状态，直到用户使用 Cmd + Q 明确退出
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在 macOS 上，当单击 dock 图标并且没有其他窗口打开时，通常会在应用中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 应用即将退出时的清理
app.on('before-quit', () => {
  console.log('应用即将退出，开始清理...');
  cleanupTempScreenshots();
});

// 应用退出时的清理
app.on('will-quit', () => {
  console.log('应用正在退出，最后清理...');
  cleanupTempScreenshots();
});

// IPC 处理函数
// 选择文件夹
ipcMain.handle('dialog:selectFolder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: '选择视频文件夹'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// 通用的打开对话框
ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return {
      cancelled: result.canceled,
      filePaths: result.filePaths
    };
  } catch (error) {
    console.error('打开对话框失败:', error);
    return {
      cancelled: true,
      error: error.message
    };
  }
});

// 扫描视频文件
ipcMain.handle('fs:scanVideoFiles', async (event, folderPath) => {
  const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'];
  const videoFiles = [];

  function scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          // 递归扫描子文件夹
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).toLowerCase();
          if (videoExtensions.includes(ext)) {
            const relativePath = path.relative(folderPath, fullPath);
            videoFiles.push({
              id: fullPath,
              name: item,
              path: fullPath,
              relativePath: relativePath,
              size: stat.size,
              timestamp: stat.mtime.toISOString(),
              directory: path.dirname(relativePath) || '.'
            });
          }
        }
      }
    } catch (error) {
      if (isDev) {
        console.error('扫描目录时出错:', error);
      }
    }
  }

  if (folderPath && fs.existsSync(folderPath)) {
    scanDirectory(folderPath);
  }

  // 按文件名排序
  videoFiles.sort((a, b) => a.name.localeCompare(b.name));

  return videoFiles;
});

// 获取文件信息
ipcMain.handle('fs:getFileInfo', async (event, filePath) => {
  try {
    const stat = fs.statSync(filePath);
    return {
      size: stat.size,
      created: stat.birthtime.toISOString(),
      modified: stat.mtime.toISOString(),
      accessed: stat.atime.toISOString()
    };
  } catch (error) {
    if (isDev) {
      console.error('获取文件信息时出错:', error);
    }
    return null;
  }
});

// 获取视频文件的安全URL
ipcMain.handle('fs:getVideoUrl', async (event, filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      // 直接返回文件协议URL
      return `file://${filePath}`;
    }
    return null;
  } catch (error) {
    if (isDev) {
      console.error('获取视频URL时出错:', error);
    }
    return null;
  }
});

// 创建ExifTool实例
let exifTool = null;
let windowsExifToolWrapper = null;

// 初始化ExifTool
function initExifTool() {
  if (process.platform === 'win32' && app.isPackaged) {
    // Windows打包环境下使用专用修复
    if (!windowsExifToolWrapper) {
      try {
        windowsExifToolFix.initialize();
        if (windowsExifToolFix.isAvailable()) {
          windowsExifToolWrapper = windowsExifToolFix.createExifToolWrapper();
          console.log('✅ Windows ExifTool包装器初始化成功');
        } else {
          console.error('❌ Windows ExifTool不可用');
          throw new Error('Windows ExifTool不可用');
        }
      } catch (error) {
        console.error('❌ Windows ExifTool包装器初始化失败:', error.message);
        throw error;
      }
    }
    return windowsExifToolWrapper;
  } else {
    // 其他平台或开发环境使用标准ExifTool
    if (!exifTool) {
      exifTool = new ExifTool();
    }
    return exifTool;
  }
}

// ExifTool错误诊断
function diagnoseExifToolError(error) {
  console.log('\n🔍 ExifTool错误诊断:');

  if (error.code === 'ENOENT') {
    console.log('❌ 错误类型: 文件或命令不存在 (ENOENT)');
    console.log('💡 可能原因:');
    console.log('   1. ExifTool文件不存在或路径错误');
    console.log('   2. 文件没有执行权限');
    console.log('   3. 缺少必要的系统依赖');
    console.log('   4. 防病毒软件阻止执行');

    console.log('🔧 建议解决方案:');
    console.log('   1. 以管理员权限运行应用');
    console.log('   2. 将应用目录添加到防病毒软件白名单');
    console.log('   3. 检查Windows Defender设置');
    console.log('   4. 重新安装应用');
  } else if (error.code === 'EACCES') {
    console.log('❌ 错误类型: 权限拒绝 (EACCES)');
    console.log('💡 可能原因:');
    console.log('   1. 没有执行权限');
    console.log('   2. 文件被其他程序占用');

    console.log('🔧 建议解决方案:');
    console.log('   1. 以管理员权限运行应用');
    console.log('   2. 检查文件权限设置');
  } else {
    console.log(`❌ 错误类型: ${error.code || '未知错误'}`);
    console.log('💡 错误信息:', error.message);
  }

  // 检查ExifTool文件状态
  if (error.path) {
    console.log('\n📁 文件状态检查:');
    try {
      const fs = require('fs');
      if (fs.existsSync(error.path)) {
        const stats = fs.statSync(error.path);
        console.log(`   文件存在: ✅`);
        console.log(`   文件大小: ${stats.size} bytes`);
        console.log(`   修改时间: ${stats.mtime.toLocaleString()}`);
        console.log(`   是否为文件: ${stats.isFile()}`);
      } else {
        console.log(`   文件存在: ❌`);
      }
    } catch (fsError) {
      console.log(`   文件检查失败: ${fsError.message}`);
    }
  }

  console.log('\n📞 如需技术支持，请提供以上诊断信息。\n');
}

// 提取GPS数据
ipcMain.handle('fs:extractGPSData', async (event, filePath) => {
  const startTime = Date.now();
  logger.info('GPS', '开始提取GPS数据', { filePath });

  try {
    if (!fs.existsSync(filePath)) {
      const error = `文件不存在: ${filePath}`;
      console.error('❌', error);
      logger.error('GPS', error);
      return null;
    }

    console.log('🔍 开始提取GPS数据:', filePath);
    logger.info('GPS', '文件存在，开始提取', {
      filePath,
      fileSize: fs.statSync(filePath).size
    });

    // 直接使用ExifTool提取GPS数据（VIOFO格式）
    // FFmpeg无法提取VIOFO的自定义GPS格式，所以直接使用ExifTool
    let gpsTrack = await extractGPSDataWithExifTool(filePath);

    if (!gpsTrack) {
      const message = '未找到GPS数据';
      console.log('⚠️', message);
      logger.warn('GPS', message, { filePath });
      return null;
    }

    // 将WGS84坐标转换为GCJ-02坐标（适配高德地图）
    const convertedTrack = convertGpsTrack(gpsTrack);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const pointCount = gpsTrack.points.length;

    const successMessage = `提取到${pointCount}个GPS点，并已转换为GCJ-02坐标系`;
    console.log('✅', successMessage);
    logger.info('GPS', 'GPS数据提取成功', {
      filePath,
      pointCount,
      duration: `${duration}ms`,
      originalCoordSystem: 'WGS84',
      convertedCoordSystem: 'GCJ-02'
    });

    return convertedTrack;

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error('❌ 提取GPS数据时出错:', error);
    logger.error('GPS', 'GPS数据提取失败', {
      filePath,
      duration: `${duration}ms`,
      error: error.message,
      stack: error.stack
    });
    return null;
  }
});

// ExifTool GPS提取逻辑（作为备用方案）
async function extractGPSDataWithExifTool(filePath) {
  try {
    const tool = initExifTool();

    let metadata;

    if (process.platform === 'win32' && app.isPackaged && tool.extractGPS) {
      // Windows打包环境下使用专用包装器
      console.log('🪟 使用Windows ExifTool包装器提取GPS数据...');
      console.log('🔍 文件路径:', filePath);

      const args = [
        '-n', // 数字格式输出
        '-j', // JSON格式输出
        '-ee', // 提取嵌入数据
        '-G3', // 组名格式
        '-a', // 允许重复标签
        '-u', // 提取未知标签
        '-U', // 提取未知二进制标签
        '-GPS*', // 只提取GPS相关数据
        '-CreateDate',
        '-DateTimeOriginal',
        '-ModifyDate',
        filePath
      ];

      console.log('🔍 ExifTool参数:', args.slice(0, -1).join(' '));

      try {
        const output = await tool.execute(args);
        console.log('🔍 ExifTool原始输出长度:', output.length);
        console.log('🔍 ExifTool输出预览:', output.substring(0, 200) + '...');

        const jsonData = JSON.parse(output);
        metadata = Array.isArray(jsonData) ? jsonData[0] : jsonData;

        console.log('🔍 解析后的元数据键数量:', Object.keys(metadata || {}).length);
        const gpsKeys = Object.keys(metadata || {}).filter(key => key.includes('GPS'));
        console.log('🔍 GPS相关键数量:', gpsKeys.length);
        if (gpsKeys.length > 0) {
          console.log('🔍 GPS键示例:', gpsKeys.slice(0, 5));
        }
      } catch (error) {
        console.error('❌ Windows ExifTool执行失败:', error.message);
        console.error('❌ 错误详情:', {
          errno: error.errno,
          code: error.code,
          syscall: error.syscall,
          path: error.path,
          spawnargs: error.spawnargs
        });

        // 提供详细的错误诊断
        diagnoseExifToolError(error);
        throw error;
      }
    } else {
      // 标准ExifTool
      console.log('🔧 使用标准ExifTool提取GPS数据...');
      console.log('🔍 文件路径:', filePath);

      const args = [
        '-n', // 数字格式输出
        '-j', // JSON格式输出
        '-ee', // 提取嵌入数据
        '-G3', // 组名格式
        '-a', // 允许重复标签
        '-u', // 提取未知标签
        '-U', // 提取未知二进制标签
        '-GPS*', // 只提取GPS相关数据
        '-CreateDate',
        '-DateTimeOriginal',
        '-ModifyDate'
      ];

      console.log('🔍 ExifTool参数:', args.join(' '));

      try {
        metadata = await tool.read(filePath, args);

        console.log('🔍 解析后的元数据键数量:', Object.keys(metadata || {}).length);
        const gpsKeys = Object.keys(metadata || {}).filter(key => key.includes('GPS'));
        console.log('🔍 GPS相关键数量:', gpsKeys.length);
        if (gpsKeys.length > 0) {
          console.log('🔍 GPS键示例:', gpsKeys.slice(0, 5));
        }
      } catch (error) {
        console.error('❌ 标准ExifTool执行失败:', error.message);
        console.error('❌ 错误详情:', error);
        throw error;
      }
    }

    if (isDev) {
      console.log('ExifTool提取到的原始GPS元数据:', JSON.stringify(metadata, null, 2));
    }

    // 解析GPS数据
    const gpsPoints = [];

    // VIOFO格式：GPS数据存储在Doc1, Doc2, Doc3... 等字段中
    const docKeys = Object.keys(metadata).filter(key => key.startsWith('Doc') && key.includes(':GPS'));

    // 按文档编号分组GPS数据
    const docGroups = {};
    for (const key of docKeys) {
      const match = key.match(/^(Doc\d+):/);
      if (match) {
        const docNum = match[1];
        if (!docGroups[docNum]) {
          docGroups[docNum] = {};
        }
        const fieldName = key.replace(`${docNum}:`, '');
        docGroups[docNum][fieldName] = metadata[key];
      }
    }

    // 将每个文档组转换为GPS点
    const docNumbers = Object.keys(docGroups).sort((a, b) => {
      const numA = parseInt(a.replace('Doc', ''));
      const numB = parseInt(b.replace('Doc', ''));
      return numA - numB;
    });

    for (const docNum of docNumbers) {
      const docData = docGroups[docNum];

      if (docData.GPSLatitude && docData.GPSLongitude) {
        const point = {
          latitude: parseFloat(docData.GPSLatitude),
          longitude: parseFloat(docData.GPSLongitude),
          altitude: docData.GPSAltitude ? parseFloat(docData.GPSAltitude) : undefined,
          speed: docData.GPSSpeed ? parseFloat(docData.GPSSpeed) : undefined,
          heading: docData.GPSTrack ? parseFloat(docData.GPSTrack) : undefined,
          timestamp: docData.GPSDateTime ? (typeof docData.GPSDateTime === 'object' ? docData.GPSDateTime.rawValue || docData.GPSDateTime : docData.GPSDateTime) : undefined,
          accuracy: docData.GPSHPositioningError ? parseFloat(docData.GPSHPositioningError) : undefined
        };
        gpsPoints.push(point);
      }
    }

    // 如果没有找到Doc格式的数据，尝试主GPS字段
    if (gpsPoints.length === 0 && metadata.GPSLatitude && metadata.GPSLongitude) {
      const point = {
        latitude: parseFloat(metadata.GPSLatitude),
        longitude: parseFloat(metadata.GPSLongitude),
        altitude: metadata.GPSAltitude ? parseFloat(metadata.GPSAltitude) : undefined,
        speed: metadata.GPSSpeed ? parseFloat(metadata.GPSSpeed) : undefined,
        heading: metadata.GPSImgDirection ? parseFloat(metadata.GPSImgDirection) : undefined,
        timestamp: metadata.GPSDateTime || metadata.CreateDate || metadata.DateTimeOriginal,
        accuracy: metadata.GPSHPositioningError ? parseFloat(metadata.GPSHPositioningError) : undefined
      };
      gpsPoints.push(point);
    }

    if (gpsPoints.length === 0) {
      if (isDev) {
        console.log('ExifTool未找到GPS数据');
      }
      return null;
    }

    // 计算轨迹统计信息
    const statistics = calculateGPSStatistics(gpsPoints);
    const track = {
      points: gpsPoints,
      totalDistance: statistics.totalDistance,
      duration: statistics.duration,
      averageSpeed: statistics.averageSpeed,
      maxSpeed: statistics.maxSpeed,
      minSpeed: statistics.minSpeed,
      startTime: gpsPoints[0]?.timestamp,
      endTime: gpsPoints[gpsPoints.length - 1]?.timestamp,
      pointCount: gpsPoints.length,
      statistics: statistics
    };

    if (isDev) {
      console.log(`ExifTool提取到${gpsPoints.length}个GPS点`);
    }
    return track;

  } catch (error) {
    if (isDev) {
      console.error('ExifTool提取GPS数据时出错:', error);
    }
    return null;
  }
}

// 生成视频缩略图
ipcMain.handle('video:generateThumbnails', async (event, videoPath, options = {}) => {
  try {
    if (isDev) {
      console.log('开始生成缩略图:', videoPath);
    }
    const thumbnails = await generateThumbnails(videoPath, options);
    if (isDev) {
      console.log(`缩略图生成完成，共 ${thumbnails.length} 个`);
    }
    return thumbnails;
  } catch (error) {
    if (isDev) {
      console.error('生成缩略图失败:', error);
    }
    return [];
  }
});

// 生成单个缩略图
ipcMain.handle('video:generateThumbnail', async (event, videoPath, timestamp, options = {}) => {
  try {
    const thumbnailPath = await generateThumbnail(videoPath, timestamp, options);
    return {
      time: timestamp,
      path: thumbnailPath,
      url: `file://${thumbnailPath}`
    };
  } catch (error) {
    if (isDev) {
      console.error('生成单个缩略图失败:', error);
    }
    return null;
  }
});

// 获取视频时长
ipcMain.handle('video:getDuration', async (event, videoPath) => {
  try {
    const duration = await getVideoDuration(videoPath);
    return duration;
  } catch (error) {
    if (isDev) {
      console.error('获取视频时长失败:', error);
    }
    return 0;
  }
});

// 预加载缩略图（异步）
ipcMain.handle('video:preloadThumbnails', async (event, videoPath, options = {}) => {
  try {
    // 异步预加载，立即返回
    preloadThumbnails(videoPath, options);
    return true;
  } catch (error) {
    if (isDev) {
      console.error('预加载缩略图失败:', error);
    }
    return false;
  }
});

// 获取缓存统计
ipcMain.handle('video:getCacheStats', async (event) => {
  try {
    return getCacheStats();
  } catch (error) {
    if (isDev) {
      console.error('获取缓存统计失败:', error);
    }
    return { fileCount: 0, totalSize: 0 };
  }
});

// 清理缓存
ipcMain.handle('video:cleanupCache', async (event, maxAge) => {
  try {
    cleanupCache(maxAge);
    return true;
  } catch (error) {
    if (isDev) {
      console.error('清理缓存失败:', error);
    }
    return false;
  }
});

// 显示Windows FFmpeg错误对话框
function showWindowsFFmpegErrorDialog(operation, error) {
  if (process.platform !== 'win32') return;

  const isEFTYPE = error.message.includes('EFTYPE') || error.code === 'EFTYPE';

  if (isEFTYPE) {
    const { dialog } = require('electron');

    dialog.showErrorBox(
      `${operation}功能暂时不可用`,
      `检测到FFmpeg执行错误，这通常由以下原因引起：

• FFmpeg文件损坏或不完整
• 缺少Microsoft Visual C++ Redistributable
• 防病毒软件阻止程序执行
• 文件权限不足

建议解决方案：
1. 重新安装应用程序
2. 安装Microsoft Visual C++ Redistributable
3. 将应用添加到防病毒软件白名单
4. 以管理员权限运行应用

如问题持续，请联系技术支持。`
    );
  }
}

// 截取视频帧
ipcMain.handle('video:captureFrame', async (event, videoPath, timestamp, options = {}) => {
  try {
    if (isDev) {
      console.log('开始截取视频帧:', videoPath, 'at', timestamp, 's');
    }

    // Windows平台诊断信息
    if (process.platform === 'win32' && !isDev) {
      console.log('🪟 Windows截图诊断信息:');
      console.log('  视频文件:', videoPath);
      console.log('  视频文件存在:', fs.existsSync(videoPath));
      console.log('  时间戳:', timestamp);
      console.log('  选项:', JSON.stringify(options, null, 2));

      if (windowsClipFix.isAvailable()) {
        const paths = windowsClipFix.getFFmpegPaths();
        console.log('  FFmpeg路径:', paths.ffmpegPath);
        console.log('  FFmpeg存在:', fs.existsSync(paths.ffmpegPath));
        console.log('  临时目录:', paths.tempDir);
        console.log('  临时目录存在:', fs.existsSync(paths.tempDir));
      }
    }

    const result = await captureFrame(videoPath, timestamp, options);
    if (isDev) {
      console.log('截图完成:', result);
    }
    return result;
  } catch (error) {
    console.error('截取视频帧失败:', error);

    // Windows特定错误处理
    if (process.platform === 'win32' && !isDev) {
      console.error('🪟 Windows截图错误详情:', error.stack);
      if (windowsClipFix.isAvailable()) {
        windowsClipFix.logDiagnosticInfo();
      }

      // 显示用户友好的错误对话框
      showWindowsFFmpegErrorDialog('截图', error);
    }

    return { success: false, error: error.message };
  }
});

// 显示保存对话框
ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
  try {
    const { dialog } = require('electron');
    const result = await dialog.showSaveDialog(options);
    return result;
  } catch (error) {
    console.error('显示保存对话框失败:', error);
    return { canceled: true, error: error.message };
  }
});

// 在文件管理器中显示文件
ipcMain.handle('shell:showItemInFolder', async (event, filePath) => {
  try {
    const { shell } = require('electron');
    shell.showItemInFolder(filePath);
    return { success: true };
  } catch (error) {
    console.error('在文件管理器中显示文件失败:', error);
    return { success: false, error: error.message };
  }
});

// 打开图片预览窗口
ipcMain.handle('image:openPreview', async (event, imagePath, imageInfo = {}) => {
  try {
    if (isDev) {
      console.log('打开图片预览:', imagePath);
    }

    // 检查文件是否存在
    if (!fs.existsSync(imagePath)) {
      throw new Error('图片文件不存在');
    }

    const window = createImagePreviewWindow(imagePath, imageInfo);
    return { success: true, windowId: window.id };
  } catch (error) {
    console.error('打开图片预览失败:', error);
    return { success: false, error: error.message };
  }
});

// 从base64数据打开图片预览窗口
ipcMain.handle('image:openPreviewFromBase64', async (event, base64Data, imageInfo = {}) => {
  try {
    if (isDev) {
      console.log('从base64数据打开图片预览');
    }

    // 创建临时文件
    const tempDir = path.join(require('os').tmpdir(), 'meea-image-preview');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempImagePath = path.join(tempDir, `preview-${Date.now()}.png`);

    // 将base64数据转换为Buffer并保存
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64String, 'base64');
    fs.writeFileSync(tempImagePath, imageBuffer);

    // 创建预览窗口
    const window = createImagePreviewWindow(tempImagePath, imageInfo);

    // 窗口关闭时清理临时文件
    window.on('closed', () => {
      try {
        if (fs.existsSync(tempImagePath)) {
          fs.unlinkSync(tempImagePath);
        }
      } catch (error) {
        console.error('清理临时预览文件失败:', error);
      }
    });

    return { success: true, windowId: window.id };
  } catch (error) {
    console.error('从base64数据打开图片预览失败:', error);
    return { success: false, error: error.message };
  }
});

// 保存base64图片数据
ipcMain.handle('image:saveBase64', async (event, base64Data, defaultFilename) => {
  try {
    if (isDev) {
      console.log('保存base64图片:', defaultFilename);
    }

    // 显示保存对话框
    const result = await dialog.showSaveDialog({
      title: '保存截图',
      defaultPath: defaultFilename,
      filters: [
        { name: 'PNG图片', extensions: ['png'] },
        { name: 'JPEG图片', extensions: ['jpg', 'jpeg'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    });

    if (result.canceled) {
      return { success: false, cancelled: true };
    }

    // 将base64数据转换为Buffer
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64String, 'base64');

    // 保存文件
    fs.writeFileSync(result.filePath, imageBuffer);

    if (isDev) {
      console.log('base64图片保存成功:', result.filePath);
    }

    return { success: true, filePath: result.filePath };
  } catch (error) {
    console.error('保存base64图片失败:', error);
    return { success: false, error: error.message };
  }
});

// 获取建议的保存路径
ipcMain.handle('file:getSuggestedPaths', async (event, options) => {
  try {
    const { app } = require('electron');
    const os = require('os');

    const { videoName, timestamp } = options;
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}-${secs.toString().padStart(2, '0')}`;
    };

    const timeStr = formatTime(timestamp || 0);
    const fileName = `screenshot_${videoName}_${timeStr}.png`;

    const desktopPath = path.join(os.homedir(), 'Desktop', fileName);
    const documentsPath = path.join(os.homedir(), 'Documents', 'Screenshots', fileName);

    return {
      default: '', // 默认路径由后端处理
      desktop: desktopPath,
      documents: documentsPath
    };
  } catch (error) {
    console.error('获取建议路径失败:', error);
    return {
      default: '',
      desktop: '',
      documents: ''
    };
  }
});

// 剪辑视频片段
ipcMain.handle('video:clipVideo', async (event, params) => {
  try {
    if (isDev) {
      console.log('开始剪辑视频:', params);
    }

    // Windows平台诊断信息
    if (process.platform === 'win32' && !isDev) {
      console.log('🪟 Windows剪辑诊断信息:');
      console.log('  输入文件:', params.inputPath);
      console.log('  输入文件存在:', fs.existsSync(params.inputPath));
      console.log('  开始时间:', params.startTime);
      console.log('  结束时间:', params.endTime);
      console.log('  输出路径:', params.outputPath);

      if (windowsClipFix.isAvailable()) {
        const paths = windowsClipFix.getFFmpegPaths();
        console.log('  FFmpeg路径:', paths.ffmpegPath);
        console.log('  FFmpeg存在:', fs.existsSync(paths.ffmpegPath));
      }
    }

    // 创建进度回调函数
    const progressCallback = (progress) => {
      // 向渲染进程发送进度更新
      event.sender.send('video:clipProgress', progress);
      if (params.onProgress) {
        params.onProgress(progress);
      }
    };

    const result = await clipVideo({
      ...params,
      onProgress: progressCallback
    });

    if (isDev) {
      console.log('剪辑完成:', result);
    }
    return result;
  } catch (error) {
    console.error('剪辑视频失败:', error);

    // Windows特定错误处理
    if (process.platform === 'win32' && !isDev) {
      console.error('🪟 Windows剪辑错误详情:', error.stack);
      if (windowsClipFix.isAvailable()) {
        windowsClipFix.logDiagnosticInfo();
      }

      // 显示用户友好的错误对话框
      showWindowsFFmpegErrorDialog('剪辑', error);
    }

    return { success: false, error: error.message };
  }
});

// 多视频合成截图
ipcMain.handle('video:captureMultiVideoFrame', async (event, params) => {
  try {
    if (isDev) {
      console.log('开始多视频合成截图:', params);
    }

    const result = await captureMultiVideoFrame(params);

    if (isDev) {
      console.log('多视频合成截图完成:', result);
    }

    return result;
  } catch (error) {
    if (isDev) {
      console.error('多视频合成截图失败:', error);
    }

    return { success: false, error: error.message };
  }
});

// 多视频合成剪辑
ipcMain.handle('video:clipMultiVideo', async (event, params) => {
  try {
    if (isDev) {
      console.log('开始多视频合成剪辑:', params);
    }

    // 创建进度回调函数
    const progressCallback = (progress) => {
      // 向渲染进程发送进度更新
      event.sender.send('video:clipMultiVideoProgress', progress);
      if (params.onProgress) {
        params.onProgress(progress);
      }
    };

    const result = await clipMultiVideo({
      ...params,
      onProgress: progressCallback
    });

    if (isDev) {
      console.log('多视频合成剪辑完成:', result);
    }

    return result;
  } catch (error) {
    if (isDev) {
      console.error('多视频合成剪辑失败:', error);
    }

    return { success: false, error: error.message };
  }
});

// 获取视频信息
ipcMain.handle('video:getInfo', async (event, videoPath) => {
  try {
    const info = await getVideoInfo(videoPath);
    return { success: true, info };
  } catch (error) {
    if (isDev) {
      console.error('获取视频信息失败:', error);
    }
    return { success: false, error: error.message };
  }
});

// 计算两点间距离（使用Haversine公式）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * 解析GPS时间戳
 * @param {string|object} timestamp 时间戳
 * @returns {Date|null} 解析后的日期对象
 */
function parseGPSTimestamp(timestamp) {
  if (!timestamp) return null;

  try {
    // 如果是对象，尝试获取rawValue或直接使用
    if (typeof timestamp === 'object') {
      timestamp = timestamp.rawValue || timestamp.toString();
    }

    // 如果是字符串，尝试解析
    if (typeof timestamp === 'string') {
      // 处理常见的GPS时间格式
      // 例如: "2023:12:25 14:30:45Z" 或 "2023-12-25T14:30:45.000Z"
      let dateStr = timestamp;

      // 将EXIF格式转换为ISO格式
      if (dateStr.includes(':') && !dateStr.includes('T')) {
        dateStr = dateStr.replace(/^(\d{4}):(\d{2}):(\d{2})/, '$1-$2-$3');
        if (!dateStr.includes('T') && dateStr.includes(' ')) {
          dateStr = dateStr.replace(' ', 'T');
        }
      }

      // 确保有时区信息
      if (!dateStr.includes('Z') && !dateStr.includes('+') && !dateStr.includes('-', 10)) {
        dateStr += 'Z';
      }

      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    }

    // 如果是数字，假设是Unix时间戳
    if (typeof timestamp === 'number') {
      return new Date(timestamp * 1000);
    }

    return null;
  } catch (error) {
    console.warn('时间戳解析失败:', timestamp, error);
    return null;
  }
}

/**
 * 计算GPS轨迹的完整统计信息
 * @param {Array} points GPS点数组
 * @returns {Object} 统计信息对象
 */
function calculateGPSStatistics(points) {
  if (!points || points.length === 0) {
    return {
      totalDistance: 0,
      duration: 0,
      averageSpeed: 0,
      maxSpeed: 0,
      minSpeed: 0,
      speedFromGPS: 0,
      speedFromDistance: 0,
      validSpeedPoints: 0,
      totalPoints: 0
    };
  }

  if (points.length === 1) {
    const singlePoint = points[0];
    return {
      totalDistance: 0,
      duration: 0,
      averageSpeed: singlePoint.speed || 0,
      maxSpeed: singlePoint.speed || 0,
      minSpeed: singlePoint.speed || 0,
      speedFromGPS: singlePoint.speed || 0,
      speedFromDistance: 0,
      validSpeedPoints: singlePoint.speed ? 1 : 0,
      totalPoints: 1
    };
  }

  // 计算总距离和时间
  let totalDistance = 0;
  let duration = 0;

  // 速度统计
  let maxSpeed = 0;
  let minSpeed = Number.MAX_VALUE;
  let totalGPSSpeed = 0;
  let validSpeedPoints = 0;

  // 计算时间范围
  const validTimePoints = points.filter(p => p.timestamp).map(p => ({
    ...p,
    parsedTime: parseGPSTimestamp(p.timestamp)
  })).filter(p => p.parsedTime);

  if (validTimePoints.length >= 2) {
    try {
      const startTime = validTimePoints[0].parsedTime;
      const endTime = validTimePoints[validTimePoints.length - 1].parsedTime;
      duration = Math.max(0, (endTime - startTime) / 1000); // 秒数

      if (isDev) {
        console.log(`GPS时间范围: ${startTime.toISOString()} 到 ${endTime.toISOString()}, 持续时间: ${duration}秒`);
      }
    } catch (error) {
      console.warn('时间解析失败:', error);
      duration = 0;
    }
  } else {
    if (isDev) {
      console.log(`有效时间点数量不足: ${validTimePoints.length}, 无法计算持续时间`);
    }
  }

  // 遍历所有点计算距离和速度统计
  for (let i = 0; i < points.length; i++) {
    const point = points[i];

    // 计算距离（从第二个点开始）
    if (i > 0) {
      const prevPoint = points[i - 1];
      if (prevPoint.latitude && prevPoint.longitude && point.latitude && point.longitude) {
        const segmentDistance = calculateDistance(
          prevPoint.latitude, prevPoint.longitude,
          point.latitude, point.longitude
        );
        totalDistance += segmentDistance;
      }
    }

    // 统计GPS记录的速度
    if (point.speed !== undefined && point.speed !== null && !isNaN(point.speed)) {
      const speed = Math.abs(point.speed); // 确保速度为正数
      totalGPSSpeed += speed;
      validSpeedPoints++;
      maxSpeed = Math.max(maxSpeed, speed);
      minSpeed = Math.min(minSpeed, speed);
    }
  }

  // 如果没有有效的速度点，重置最小速度
  if (validSpeedPoints === 0) {
    minSpeed = 0;
  }

  // 计算平均速度
  const speedFromGPS = validSpeedPoints > 0 ? totalGPSSpeed / validSpeedPoints : 0;
  const speedFromDistance = duration > 0 ? (totalDistance / 1000) / (duration / 3600) : 0; // km/h

  // 选择更可靠的平均速度
  const averageSpeed = validSpeedPoints > points.length * 0.5 ? speedFromGPS : speedFromDistance;

  return {
    totalDistance: Math.round(totalDistance), // 米
    duration: Math.round(duration), // 秒
    averageSpeed: Math.round(averageSpeed * 10) / 10, // km/h，保留1位小数
    maxSpeed: Math.round(maxSpeed * 10) / 10, // km/h，保留1位小数
    minSpeed: Math.round(minSpeed * 10) / 10, // km/h，保留1位小数
    speedFromGPS: Math.round(speedFromGPS * 10) / 10, // 从GPS记录计算的平均速度
    speedFromDistance: Math.round(speedFromDistance * 10) / 10, // 从距离计算的平均速度
    validSpeedPoints: validSpeedPoints, // 有效速度点数量
    totalPoints: points.length // 总点数
  };
}

// 计算两点间距离（米）- Haversine公式
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// 窗口控制
ipcMain.handle('window:minimize', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window:maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window:close', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// 全屏控制
ipcMain.handle('window:toggleFullscreen', () => {
  if (mainWindow) {
    if (mainWindow.isFullScreen()) {
      mainWindow.setFullScreen(false);
    } else {
      mainWindow.setFullScreen(true);
    }
    return mainWindow.isFullScreen();
  }
  return false;
});

ipcMain.handle('window:isFullscreen', () => {
  if (mainWindow) {
    return mainWindow.isFullScreen();
  }
  return false;
});

// 配置管理相关的IPC处理函数
ipcMain.handle('config:load', () => {
  return loadConfig();
});

ipcMain.handle('config:save', (event, config) => {
  return saveConfig(config);
});

ipcMain.handle('config:getLastVideoFolder', () => {
  const config = loadConfig();
  return config.lastVideoFolder;
});

ipcMain.handle('config:setLastVideoFolder', (event, folderPath) => {
  const config = loadConfig();
  config.lastVideoFolder = folderPath;
  return saveConfig(config);
});

ipcMain.handle('config:getAmapApiKey', () => {
  const config = loadConfig();
  return config.amapApiKey;
});

ipcMain.handle('config:setAmapApiKey', (event, apiKey) => {
  const config = loadConfig();
  config.amapApiKey = apiKey;
  return saveConfig(config);
});

// 打开外部链接
ipcMain.handle('shell:openExternal', async (event, url) => {
  try {
    await shell.openExternal(url);
    return true;
  } catch (error) {
    if (isDev) {
      console.error('打开外部链接失败:', error);
    }
    return false;
  }
});

// 显示机器码对话框
async function showMachineIdDialog() {
  try {
    const machineId = await machineIdGenerator.getMachineId();
    const formattedId = machineIdGenerator.formatMachineId(machineId);

    const result = await dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '机器码信息',
      message: '设备机器码',
      detail: `原始机器码: ${machineId}\n\n格式化机器码: ${formattedId}\n\n机器码是基于您的硬件特征生成的唯一标识符，用于软件授权和技术支持。`,
      buttons: ['复制原始机器码', '复制格式化机器码', '关闭'],
      defaultId: 2,
      cancelId: 2
    });

    // 处理用户选择
    if (result.response === 0) {
      // 复制原始机器码
      clipboard.writeText(machineId);
      if (isDev) {
        console.log('已复制原始机器码到剪贴板');
      }
    } else if (result.response === 1) {
      // 复制格式化机器码
      clipboard.writeText(formattedId);
      if (isDev) {
        console.log('已复制格式化机器码到剪贴板');
      }
    }
  } catch (error) {
    if (isDev) {
      console.error('显示机器码对话框失败:', error);
    }
    dialog.showErrorBox('错误', '获取机器码失败，请重试。');
  }
}

// 设置应用菜单 - 保留标题栏但隐藏功能菜单
app.whenReady().then(async () => {
  // 在应用准备就绪时创建最小应用菜单
  createMinimalAppMenu();

  console.log('✅ 已设置最小应用菜单，保留标题栏，隐藏功能菜单 (File, Edit 等)');

  // 注册全局快捷键
  if (isDebugMode) {
    // 调试模式下的快捷键
    try {
      const success = globalShortcut.register('CommandOrControl+Shift+L', () => {
        console.log('🔍 [DEBUG] 快捷键 Ctrl+Shift+L 被触发');
        if (mainWindow) {
          createLogViewerWindow();
        } else {
          console.log('⚠️ 主窗口不存在，无法创建日志查看器');
        }
      });

      if (success) {
        console.log('🐛 已注册调试快捷键: Ctrl+Shift+L (查看日志)');
      } else {
        console.log('❌ 快捷键注册失败: Ctrl+Shift+L');
      }
    } catch (error) {
      console.error('❌ 快捷键注册异常:', error);
    }

    // 测试快捷键（仅调试模式）
    try {
      const testSuccess = globalShortcut.register('CommandOrControl+Shift+T', () => {
        console.log('🧪 测试快捷键被触发');
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '快捷键测试',
          message: '快捷键系统正常工作！',
          detail: `测试时间: ${new Date().toLocaleString()}\n` +
                  `调试模式: ${isDebugMode}\n` +
                  `应用名称: ${app.getName ? app.getName() : 'N/A'}`
        });
      });

      if (testSuccess) {
        console.log('🧪 测试快捷键已注册: Ctrl+Shift+T');
      }
    } catch (error) {
      console.error('❌ 测试快捷键注册失败:', error);
    }
  } else {
    console.log('⚠️ 生产环境，跳过调试快捷键注册');
  }

  // 注册查看机器码快捷键（所有环境都可用）
  try {
    const machineIdSuccess = globalShortcut.register('CommandOrControl+Shift+M', () => {
      if (mainWindow) {
        showMachineIdDialog();
        console.log('🔑 机器码查看快捷键被触发');
      }
    });

    if (machineIdSuccess) {
      console.log('🔑 已注册机器码查看快捷键: Ctrl+Shift+M');
    } else {
      console.log('❌ 机器码快捷键注册失败: Ctrl+Shift+M');
    }
  } catch (error) {
    console.error('❌ 机器码快捷键注册异常:', error);
  }
});

// ==================== 日志查看器相关 IPC 处理函数 ====================

// 获取日志内容
ipcMain.handle('log:getContent', async () => {
  try {
    if (!logger.isInitialized) {
      return '日志系统未初始化';
    }

    const currentLogFile = logger.getCurrentLogFile();
    if (!currentLogFile || !fs.existsSync(currentLogFile)) {
      return '日志文件不存在';
    }

    // 读取日志文件内容
    const logContent = fs.readFileSync(currentLogFile, 'utf8');
    return logContent;
  } catch (error) {
    console.error('读取日志文件失败:', error);
    return `读取日志失败: ${error.message}`;
  }
});

// 打开日志文件夹
ipcMain.handle('log:openFolder', async () => {
  try {
    const logDir = logger.getLogDirectory();
    if (logDir && fs.existsSync(logDir)) {
      await shell.openPath(logDir);
      return { success: true };
    } else {
      throw new Error('日志目录不存在');
    }
  } catch (error) {
    console.error('打开日志目录失败:', error);
    return { success: false, error: error.message };
  }
});

// 导出日志
ipcMain.handle('log:export', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: '导出日志文件',
      defaultPath: `meea-viofo-logs-${new Date().toISOString().slice(0, 10)}.txt`,
      filters: [
        { name: '文本文件', extensions: ['txt'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    });

    if (result.canceled) {
      return { success: false, error: '用户取消' };
    }

    const currentLogFile = logger.getCurrentLogFile();
    if (!currentLogFile || !fs.existsSync(currentLogFile)) {
      throw new Error('日志文件不存在');
    }

    // 复制日志文件到指定位置
    fs.copyFileSync(currentLogFile, result.filePath);

    return { success: true, path: result.filePath };
  } catch (error) {
    console.error('导出日志失败:', error);
    return { success: false, error: error.message };
  }
});

// 打开日志查看器窗口
ipcMain.handle('log:openViewer', () => {
  createLogViewerWindow();
});

// ==================== 许可证相关 IPC 处理函数 ====================

// 请求许可证
ipcMain.handle('license:request', async (event, verificationCode) => {
  try {
    if (!licenseService) {
      throw new Error('系统初始化失败');
    }

    // 获取机器码
    const machineId = await machineIdGenerator.getMachineId();

    // 请求许可证
    const success = await licenseService.requestLicense(verificationCode, machineId);

    return {
      success: success,
      message: success ? '验证成功' : '验证码错误，请重试'
    };
  } catch (error) {
    if (isDev) {
      console.error('请求许可证失败:', error);
    }
    return {
      success: false,
      message: '验证码错误，请重试'
    };
  }
});

// 验证本地许可证
ipcMain.handle('license:validate', async (event) => {
  try {
    if (!licenseService) {
      throw new Error('许可证服务未初始化');
    }

    // 获取机器码
    const machineId = await machineIdGenerator.getMachineId();

    // 验证本地许可证
    const isValid = await licenseService.validateLocalCertificate(machineId);

    return {
      success: true,
      isValid: isValid,
      message: isValid ? '许可证有效' : '许可证无效或已过期'
    };
  } catch (error) {
    if (isDev) {
      console.error('验证许可证失败:', error);
    }
    return {
      success: false,
      isValid: false,
      message: error.message || '许可证验证过程中发生错误'
    };
  }
});

// 获取许可证信息
ipcMain.handle('license:getInfo', async (event) => {
  try {
    if (!licenseService) {
      throw new Error('许可证服务未初始化');
    }

    // 获取机器码
    const machineId = await machineIdGenerator.getMachineId();

    // 获取许可证信息
    const info = licenseService.getCertificateInfo(machineId);

    return {
      success: true,
      info: info,
      message: info ? '许可证信息获取成功' : '未找到有效许可证'
    };
  } catch (error) {
    if (isDev) {
      console.error('获取许可证信息失败:', error);
    }
    return {
      success: false,
      info: null,
      message: error.message || '获取许可证信息时发生错误'
    };
  }
});

// 清除许可证
ipcMain.handle('license:clear', async (event) => {
  try {
    if (!licenseService) {
      throw new Error('许可证服务未初始化');
    }

    // 清除许可证
    licenseService.clearCertificate();

    return {
      success: true,
      message: '许可证已清除'
    };
  } catch (error) {
    if (isDev) {
      console.error('清除许可证失败:', error);
    }
    return {
      success: false,
      message: error.message || '清除许可证时发生错误'
    };
  }
});

// 获取机器码（用于许可证系统）
ipcMain.handle('license:getMachineId', async (event) => {
  try {
    const machineId = await machineIdGenerator.getMachineId();
    return {
      success: true,
      machineId: machineId
    };
  } catch (error) {
    if (isDev) {
      console.error('获取机器码失败:', error);
    }
    return {
      success: false,
      machineId: null,
      message: error.message || '获取机器码时发生错误'
    };
  }
});

// 获取日志信息
ipcMain.handle('logger:getInfo', async () => {
  try {
    return {
      success: true,
      logDirectory: logger.getLogDirectory(),
      currentLogFile: logger.getCurrentLogFile(),
      allLogFiles: logger.getAllLogFiles()
    };
  } catch (error) {
    console.error('获取日志信息失败:', error);
    return {
      success: false,
      message: error.message || '获取日志信息时发生错误'
    };
  }
});

// 打开日志目录
ipcMain.handle('logger:openDirectory', async () => {
  try {
    const logDir = logger.getLogDirectory();
    if (logDir && fs.existsSync(logDir)) {
      await shell.openPath(logDir);
      logger.info('System', '用户打开了日志目录', logDir);
      return { success: true };
    }
    return { success: false, message: '日志目录不存在' };
  } catch (error) {
    console.error('打开日志目录失败:', error);
    return {
      success: false,
      message: error.message || '打开日志目录时发生错误'
    };
  }
});

// 打开当前日志文件
ipcMain.handle('logger:openCurrentFile', async () => {
  try {
    const logFile = logger.getCurrentLogFile();
    if (logFile && fs.existsSync(logFile)) {
      await shell.openPath(logFile);
      logger.info('System', '用户打开了当前日志文件', logFile);
      return { success: true };
    }
    return { success: false, message: '当前日志文件不存在' };
  } catch (error) {
    console.error('打开日志文件失败:', error);
    return {
      success: false,
      message: error.message || '打开日志文件时发生错误'
    };
  }
});

// 调试相关的 IPC 处理程序
ipcMain.handle('debug:getInfo', () => {
  return {
    version: buildInfo.version,
    buildTime: buildInfo.buildTime,
    platform: `${process.platform} ${process.arch}`,
    debugMode: isDebugMode
  };
});

ipcMain.handle('debug:openDevTools', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.openDevTools();
    return { success: true, message: 'Chrome 开发者工具已打开' };
  }
  return { success: false, message: '主窗口不存在' };
});
