// 简化的 Toaster 组件，避免 Chakra UI v3 兼容性问题
import React from 'react';

// 简单的 toaster 实现，兼容 create API
export const toaster = {
  create: (options: { description: string; status: string; duration?: number }) => {
    const { description, status } = options;
    switch (status) {
      case 'success':
        console.log('✅ Success:', description);
        break;
      case 'error':
        console.error('❌ Error:', description);
        break;
      case 'warning':
        console.warn('⚠️ Warning:', description);
        break;
      case 'info':
      default:
        console.log('ℹ️ Info:', description);
        break;
    }
    // 可以在这里添加简单的通知逻辑
  },
  success: (message: string) => {
    console.log('✅ Success:', message);
  },
  error: (message: string) => {
    console.error('❌ Error:', message);
  },
  info: (message: string) => {
    console.log('ℹ️ Info:', message);
  },
  dismiss: (id?: string) => {
    console.log('Dismiss toast:', id);
  }
};

// 简化的 Toaster 组件
export const Toaster = () => {
  // 暂时返回空组件，避免 Chakra UI v3 兼容性问题
  return null;
};
