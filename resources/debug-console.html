<!DOCTYPE html>
<html>
<head>
    <title>MEEA-VIOFO Debug Console</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            background: #2d2d30;
            color: #ffffff;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-bottom: 2px solid #007acc;
        }
        .header h2 {
            margin: 0 0 10px 0;
            color: #4ec9b0;
        }
        .info {
            color: #9cdcfe;
            margin: 5px 0;
        }
        .logs {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Consolas', monospace;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-time {
            color: #6a737d;
        }
        .log-level-LOG {
            color: #58a6ff;
        }
        .log-level-ERROR {
            color: #f85149;
        }
        .log-level-WARN {
            color: #d29922;
        }
        .log-level-INFO {
            color: #7c3aed;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-family: inherit;
        }
        button:hover {
            background: #2ea043;
        }
        .clear-btn {
            background: #da3633;
        }
        .clear-btn:hover {
            background: #e5534b;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #238636;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 10px;
        }
        .status.disconnected {
            background: #da3633;
        }
    </style>
</head>
<body>
    <div class="status" id="status">🟢 Connected</div>
    
    <div class="header">
        <h2>🎉 MEEA-VIOFO Debug Console</h2>
        <div class="info">📋 Version: <span id="version">Loading...</span></div>
        <div class="info">🕒 Build Time: <span id="buildTime">Loading...</span></div>
        <div class="info">💻 Platform: <span id="platform">Loading...</span></div>
        <div class="info">🐛 Debug Mode: <span id="debugMode">Loading...</span></div>
        <div class="info">⏰ Started: <span id="startTime">Loading...</span></div>
    </div>
    
    <div class="controls">
        <button onclick="clearLogs()" class="clear-btn">🗑️ Clear Logs</button>
        <button onclick="scrollToBottom()">⬇️ Scroll to Bottom</button>
        <button onclick="toggleAutoScroll()">🔄 Auto Scroll: <span id="autoScrollStatus">ON</span></button>
        <button onclick="exportLogs()">💾 Export Logs</button>
    </div>
    
    <div id="logs" class="logs">
        <div class="log-entry">
            <span class="log-time">[Loading...]</span>
            <span class="log-level-INFO">[INFO]</span>
            🎉 Debug Console initializing...
        </div>
    </div>

    <script>
        let autoScroll = true;
        let logCount = 0;
        let allLogs = [];
        
        // 初始化
        document.getElementById('startTime').textContent = new Date().toLocaleString();
        
        function addLog(level, message) {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const time = new Date().toLocaleTimeString();
            const logData = { time, level, message };
            allLogs.push(logData);
            
            logEntry.innerHTML = 
                '<span class="log-time">[' + time + ']</span> ' +
                '<span class="log-level-' + level + '">[' + level + ']</span> ' +
                escapeHtml(message);
            
            logsDiv.appendChild(logEntry);
            logCount++;
            
            // 限制日志条数，避免内存溢出
            if (logCount > 1000) {
                logsDiv.removeChild(logsDiv.firstChild);
                logCount--;
            }
            
            if (autoScroll) {
                scrollToBottom();
            }
            
            // 更新状态
            updateStatus(true);
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            logCount = 0;
            allLogs = [];
            addLog('INFO', '🗑️ Logs cleared');
        }
        
        function scrollToBottom() {
            const logsDiv = document.getElementById('logs');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            document.getElementById('autoScrollStatus').textContent = autoScroll ? 'ON' : 'OFF';
        }
        
        function exportLogs() {
            const logText = allLogs.map(log => 
                `[${log.time}] [${log.level}] ${log.message}`
            ).join('\n');
            
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `meea-viofo-debug-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.log`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            addLog('INFO', '💾 Logs exported to file');
        }
        
        function updateStatus(connected) {
            const status = document.getElementById('status');
            if (connected) {
                status.textContent = '🟢 Connected';
                status.className = 'status';
            } else {
                status.textContent = '🔴 Disconnected';
                status.className = 'status disconnected';
            }
        }
        
        function updateInfo(info) {
            if (info.version) document.getElementById('version').textContent = info.version;
            if (info.buildTime) document.getElementById('buildTime').textContent = info.buildTime;
            if (info.platform) document.getElementById('platform').textContent = info.platform;
            if (info.debugMode !== undefined) document.getElementById('debugMode').textContent = info.debugMode;
        }
        
        // 全局函数供主进程调用
        window.addConsoleLog = addLog;
        window.updateConsoleInfo = updateInfo;
        
        // 初始化消息
        setTimeout(() => {
            addLog('LOG', '=== MEEA-VIOFO Debug Console Ready ===');
            addLog('LOG', 'Waiting for application logs...');
        }, 500);
        
        // 连接状态检查
        let lastLogTime = Date.now();
        setInterval(() => {
            const now = Date.now();
            const timeSinceLastLog = now - lastLogTime;
            updateStatus(timeSinceLastLog < 30000); // 30秒内有日志则认为连接正常
        }, 5000);
        
        // 更新最后日志时间
        const originalAddLog = addLog;
        addLog = function(level, message) {
            lastLogTime = Date.now();
            return originalAddLog(level, message);
        };
    </script>
</body>
</html>
