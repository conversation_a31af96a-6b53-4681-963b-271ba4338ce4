# 视频布局稳定性修复

## 🎯 问题描述

1. **双视频比例缩放问题**：两个视频时，次视频没有按照比例缩放
2. **布局刷新问题**：视频隐藏显示时，刷新了视频的布局，影响了其他视频的位置

## 🔍 问题分析

### 1. **双视频比例缩放问题**

#### 根本原因：
- 前端使用的是左上角+下方整行的布局
- 后端使用的是上下布局
- 布局不一致导致比例计算错误

#### 代码问题：
```javascript
// 前端 MultiVideoPlayer.tsx - 错误的布局
gridTemplateColumns: '1fr 1fr',  // 两列
positions: [
  { gridColumn: '1 / -1', gridRow: '2' }, // 主视频 - 下方整行
  { gridColumn: '1', gridRow: '1' }       // 次视频 - 左上角
]

// 后端 clip-service.js - 正确的布局
// 上下布局，每个视频占满宽度
[0:v]scale=${finalWidth}:${topHeight}[v0];[1:v]scale=${finalWidth}:${bottomHeight}[v1];[v0][v1]vstack
```

### 2. **布局刷新问题**

#### 根本原因：
- 使用 `visibleVideoCount` 来计算布局
- 隐藏视频时，可见视频数量改变，导致布局重新计算
- 使用 `visibleVideoIndex` 来计算位置，导致位置偏移

#### 代码问题：
```javascript
// 错误：使用可见视频数量
const layout = getVideoLayout(visibleVideoCount);

// 错误：计算可见视频中的位置索引
const visibleVideoIndex = displayFiles
  .slice(0, displayIndex)
  .filter((f, i) => videoVisibility[...])
  .length;
```

## ✅ 解决方案

### 1. **修复双视频布局一致性**

#### A. 前端布局修复
```javascript
// 修复前：左上角+下方整行布局
} else if (count === 2) {
  return {
    gridTemplateColumns: '1fr 1fr',
    gridTemplateRows: 'auto auto',
    positions: [
      { gridColumn: '1 / -1', gridRow: '2' }, // 主视频 - 下方整行
      { gridColumn: '1', gridRow: '1' }       // 次视频 - 左上角
    ]
  };

// 修复后：上下布局，与后端一致
} else if (count === 2) {
  return {
    gridTemplateColumns: '1fr',
    gridTemplateRows: 'auto auto',
    positions: [
      { gridColumn: '1', gridRow: '1' }, // 第一个视频 - 上方
      { gridColumn: '1', gridRow: '2' }  // 第二个视频 - 下方
    ]
  };
```

### 2. **修复布局稳定性**

#### A. 使用总视频数量而不是可见视频数量
```javascript
// 修复前：使用可见视频数量，导致布局改变
const layout = getVideoLayout(visibleVideoCount);

// 修复后：使用总视频数量，保持布局稳定
const layout = getVideoLayout(displayFiles.length);
```

#### B. 使用显示索引而不是可见视频索引
```javascript
// 修复前：计算可见视频中的位置索引
const visibleVideoIndex = displayFiles
  .slice(0, displayIndex)
  .filter((f, i) => videoVisibility[videoFiles.findIndex(vf => vf.id === f.id)])
  .length;
const position = layout.positions[visibleVideoIndex] || {};

// 修复后：直接使用显示索引
const position = layout.positions[displayIndex] || {};
```

## 📦 修复版本信息

- **修复时间**: 2025-08-03 01:45
- **修复内容**: 视频布局稳定性和双视频比例
- **影响范围**: MultiVideoPlayer.tsx

## 🎨 修复效果

### ✅ **双视频布局一致性**
```
前端显示：
┌─────────────────┐
│    视频1 (上)   │  ← 保持比例
├─────────────────┤
│    视频2 (下)   │  ← 保持比例
└─────────────────┘

后端合成：
┌─────────────────┐
│    视频1 (上)   │  ← 与前端一致
├─────────────────┤
│    视频2 (下)   │  ← 与前端一致
└─────────────────┘
```

### ✅ **布局稳定性**
```
隐藏前：
┌─────────┬─────────┐
│  视频1  │  视频2  │
├─────────┴─────────┤
│      视频3        │
└───────────────────┘

隐藏视频2后：
┌─────────┬─────────┐
│  视频1  │  [隐藏] │  ← 位置保持不变
├─────────┴─────────┤
│      视频3        │  ← 位置保持不变
└───────────────────┘
```

## 🧪 功能验证

### 测试步骤

#### 1. **双视频比例测试**
```
操作：
1. 导入两个不同分辨率的视频
2. 观察前端显示的比例
3. 执行截图或剪辑
4. 检查输出结果的比例

预期：
- 前端显示：两个视频上下排列，各自保持原始比例
- 输出结果：与前端显示完全一致
```

#### 2. **布局稳定性测试**
```
操作：
1. 导入3个视频
2. 观察初始布局
3. 隐藏中间的视频
4. 观察其他视频的位置

预期：
- 其他视频位置不变
- 只有被隐藏的视频消失
- 布局网格保持不变
```

#### 3. **视频可见性控制测试**
```
操作：
1. 隐藏和显示不同的视频
2. 观察布局是否稳定
3. 执行截图和剪辑
4. 检查结果是否正确

预期：
- 布局始终稳定
- 只有可见的视频参与截图和剪辑
- 视频顺序保持正确
```

## 🎯 技术细节

### 布局计算逻辑
```javascript
// 现在的逻辑：
1. 使用 displayFiles.length 确定布局类型
2. 布局类型一旦确定就不会改变
3. 每个视频使用固定的 displayIndex 作为位置索引
4. 隐藏的视频只是设置 display: none，不影响布局
```

### 视频显示控制
```javascript
// 显示控制逻辑：
display={!isVideoVisible ? "none" : "flex"}  // 控制显示/隐藏
position={isHiddenByFocus ? "absolute" : "relative"}  // 主视频模式
visibility={isHiddenByFocus ? "hidden" : "visible"}  // 主视频模式
```

### 前后端布局一致性
```javascript
// 双视频布局：
前端：gridTemplateColumns: '1fr', gridTemplateRows: 'auto auto'
后端：[v0][v1]vstack=inputs=2[v]  // 垂直堆叠

// 三视频布局：
前端：gridTemplateColumns: '1fr 1fr', 上方两个，下方一个
后端：[v0][v1]hstack=inputs=2[top];[top][v2]vstack=inputs=2[v]
```

## 🏆 完整修复状态

### ✅ **已解决的所有问题**
1. React 应用启动错误 ✅
2. 开发者工具无法打开 ✅
3. 快捷键无响应 ✅
4. 函数作用域错误 ✅
5. 临时目录不存在 ✅
6. FFmpeg 命令参数冲突 ✅
7. 视频拼接顺序错误 ✅
8. 视频拼接高度计算错误 ✅
9. Toaster API 兼容性错误 ✅
10. 用户可见通知缺失 ✅
11. 视频保存成功通知缺失 ✅
12. 剪辑进度显示错误 ✅
13. 剪辑视频顺序错误 ✅
14. 双视频比例缩放错误 ✅
15. 视频隐藏时布局刷新问题 ✅

### ✅ **完全正常的功能**
1. React 应用完整显示 ✅
2. 开发者工具和快捷键 ✅
3. 视频播放和操作 ✅
4. 单视频截图（带成功通知）✅
5. 截图（正确顺序+正确尺寸+成功通知）✅
6. 剪辑（正确顺序+正确尺寸+正确进度+成功通知）✅
7. 视频可见性控制（稳定布局）✅
8. 用户自定义排列 ✅
9. 智能高度计算 ✅
10. 完整的用户通知系统 ✅
11. 实时剪辑进度显示 ✅
12. 稳定的视频布局系统 ✅

---

**修复时间**: 2025-08-03 01:45  
**问题类型**: 视频布局稳定性和比例一致性  
**解决方案**: 统一前后端布局逻辑，使用固定位置索引  
**状态**: 视频布局系统完全修复
