import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Badge,
  Divider,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon
} from '@chakra-ui/react';

const { ipcRenderer } = window.require('electron');

const DebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  // 获取调试信息
  const fetchDebugInfo = async () => {
    try {
      const info = await ipcRenderer.invoke('debug:getInfo');
      setDebugInfo(info);
    } catch (error) {
      console.error('获取调试信息失败:', error);
    }
  };

  // 打开调试控制台
  const openDebugConsole = async () => {
    setLoading(true);
    try {
      const result = await ipcRenderer.invoke('debug:openConsole');
      if (result.success) {
        toast({
          title: '成功',
          description: result.message,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        fetchDebugInfo(); // 刷新信息
      } else {
        toast({
          title: '失败',
          description: result.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: '错误',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // 显示调试控制台
  const showDebugConsole = async () => {
    try {
      const result = await ipcRenderer.invoke('debug:showConsole');
      if (result.success) {
        toast({
          title: '成功',
          description: result.message,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        toast({
          title: '失败',
          description: result.message,
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: '错误',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, []);

  return (
    <Box p={6} maxW="800px" mx="auto">
      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={4}>
            🐛 调试面板
          </Text>
          <Text color="gray.600">
            这是一个调试面板，用于测试和管理调试功能。
          </Text>
        </Box>

        {debugInfo && (
          <Box>
            <Text fontSize="lg" fontWeight="semibold" mb={3}>
              📋 系统信息
            </Text>
            <VStack spacing={2} align="stretch">
              <HStack>
                <Text fontWeight="medium">版本:</Text>
                <Code>{debugInfo.version}</Code>
              </HStack>
              <HStack>
                <Text fontWeight="medium">构建时间:</Text>
                <Code>{debugInfo.buildTime}</Code>
              </HStack>
              <HStack>
                <Text fontWeight="medium">平台:</Text>
                <Code>{debugInfo.platform}</Code>
              </HStack>
              <HStack>
                <Text fontWeight="medium">调试模式:</Text>
                <Badge colorScheme={debugInfo.debugMode ? 'green' : 'red'}>
                  {debugInfo.debugMode ? '已启用' : '未启用'}
                </Badge>
              </HStack>
              <HStack>
                <Text fontWeight="medium">控制台窗口:</Text>
                <Badge colorScheme={debugInfo.consoleWindowExists ? 'green' : 'gray'}>
                  {debugInfo.consoleWindowExists ? '已创建' : '未创建'}
                </Badge>
                {debugInfo.consoleWindowExists && (
                  <Badge colorScheme={debugInfo.consoleWindowVisible ? 'green' : 'orange'}>
                    {debugInfo.consoleWindowVisible ? '可见' : '隐藏'}
                  </Badge>
                )}
              </HStack>
            </VStack>
          </Box>
        )}

        <Divider />

        <Box>
          <Text fontSize="lg" fontWeight="semibold" mb={3}>
            🛠️ 调试操作
          </Text>
          <VStack spacing={3} align="stretch">
            <HStack spacing={3}>
              <Button
                colorScheme="blue"
                onClick={openDebugConsole}
                isLoading={loading}
                loadingText="创建中..."
              >
                🪟 创建调试控制台
              </Button>
              <Button
                colorScheme="green"
                onClick={showDebugConsole}
                isDisabled={!debugInfo?.consoleWindowExists}
              >
                👁️ 显示控制台
              </Button>
              <Button
                colorScheme="gray"
                onClick={fetchDebugInfo}
              >
                🔄 刷新信息
              </Button>
            </HStack>
          </VStack>
        </Box>

        {debugInfo?.debugMode && (
          <Alert status="info">
            <AlertIcon />
            <Box>
              <AlertTitle>调试模式已启用！</AlertTitle>
              <AlertDescription>
                应用正在调试模式下运行。开发者工具应该已经自动打开，
                调试控制台窗口也应该已经创建。如果没有看到控制台窗口，
                请点击上面的按钮手动创建或显示。
              </AlertDescription>
            </Box>
          </Alert>
        )}

        <Accordion allowToggle>
          <AccordionItem>
            <h2>
              <AccordionButton>
                <Box flex="1" textAlign="left">
                  📖 使用说明
                </Box>
                <AccordionIcon />
              </AccordionButton>
            </h2>
            <AccordionPanel pb={4}>
              <VStack spacing={3} align="stretch">
                <Text>
                  <strong>1. 调试模式检查:</strong> 确认调试模式是否已启用
                </Text>
                <Text>
                  <strong>2. 创建调试控制台:</strong> 点击"创建调试控制台"按钮手动创建控制台窗口
                </Text>
                <Text>
                  <strong>3. 显示控制台:</strong> 如果控制台已创建但被隐藏，点击"显示控制台"按钮
                </Text>
                <Text>
                  <strong>4. 开发者工具:</strong> 按 F12 或 Ctrl+Shift+I 打开开发者工具
                </Text>
                <Text>
                  <strong>5. 日志查看:</strong> 在调试控制台或开发者工具的控制台中查看日志
                </Text>
              </VStack>
            </AccordionPanel>
          </AccordionItem>
        </Accordion>
      </VStack>
    </Box>
  );
};

export default DebugPanel;
