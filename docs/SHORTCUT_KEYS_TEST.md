# 快捷键测试指南

## 🎯 测试目标

验证 Windows 调试版本中的开发者工具快捷键是否正常工作。

## 📋 测试步骤

### 1. 安装调试版本
- 使用 `MEEA-VIOFO-Setup-25.7.18-1805-windows-x64.exe`
- 确认这是通过 `yarn build:windows-x64-debug` 构建的版本

### 2. 启动应用
- 启动应用后等待2-4秒
- 应该会看到：
  - 主应用窗口
  - 自动打开的 Chrome 开发者工具
  - 调试模式确认对话框

### 3. 测试快捷键

#### 测试 F12 键
1. 如果开发者工具已打开，按 F12 应该关闭它
2. 如果开发者工具已关闭，按 F12 应该打开它
3. 观察控制台是否有日志：`🛠️ [DEBUG] F12 快捷键触发，切换开发者工具`

#### 测试 Ctrl+Shift+I
1. 按住 Ctrl+Shift，然后按 I
2. 应该切换开发者工具的显示状态
3. 观察控制台是否有日志：`🛠️ [DEBUG] Ctrl+Shift+I 快捷键触发，切换开发者工具`

#### 测试 Ctrl+Shift+J
1. 按住 Ctrl+Shift，然后按 J
2. 应该强制打开开发者工具（即使已经打开）
3. 观察控制台是否有日志：`🛠️ [DEBUG] Ctrl+Shift+J 快捷键触发，打开开发者工具`

### 4. 检查快捷键注册状态

在开发者工具的控制台中查找以下日志：

```
🔧 [DEBUG] 开发者工具快捷键注册结果:
  - F12: ✅
  - Ctrl+Shift+I: ✅
  - Ctrl+Shift+J: ✅
```

## ✅ 预期结果

### 成功指标
- [ ] 应用启动时自动打开开发者工具
- [ ] F12 键能够切换开发者工具
- [ ] Ctrl+Shift+I 能够切换开发者工具
- [ ] Ctrl+Shift+J 能够打开开发者工具
- [ ] 控制台中显示快捷键触发日志
- [ ] 控制台中显示快捷键注册成功日志

### 失败指标
- [ ] 快捷键完全无响应
- [ ] 控制台中显示快捷键注册失败（❌）
- [ ] 按键时没有任何日志输出
- [ ] 开发者工具无法通过快捷键操作

## 🔍 故障排除

### 如果快捷键不工作

1. **检查调试模式**
   - 确认看到了调试模式确认对话框
   - 检查控制台是否有 `🐛 [DEBUG]` 相关日志

2. **检查快捷键注册**
   - 查找 `🔧 [DEBUG] 开发者工具快捷键注册结果` 日志
   - 确认所有快捷键都显示 ✅

3. **检查快捷键冲突**
   - 关闭其他可能占用这些快捷键的软件
   - 尝试在不同的应用状态下按快捷键

4. **重启测试**
   - 完全关闭应用
   - 重新启动应用
   - 重新测试快捷键

### 如果仍然不工作

1. **查看详细日志**
   - 在控制台中查找任何错误信息
   - 查找 `❌ 注册开发者工具快捷键失败` 相关日志

2. **手动测试**
   - 尝试在调试模式确认对话框中点击"重新打开开发者工具"
   - 检查这个按钮是否能正常工作

3. **环境检查**
   - 确认 Windows 版本兼容性
   - 检查是否有安全软件阻止快捷键注册

## 📝 测试报告模板

```
测试环境:
- Windows 版本: 
- 应用版本: MEEA-VIOFO-Setup-25.7.18-1805-windows-x64.exe
- 构建时间: 

测试结果:
- 应用启动: [ ] 成功 [ ] 失败
- 自动打开开发者工具: [ ] 成功 [ ] 失败
- F12 快捷键: [ ] 成功 [ ] 失败
- Ctrl+Shift+I: [ ] 成功 [ ] 失败
- Ctrl+Shift+J: [ ] 成功 [ ] 失败
- 快捷键注册日志: [ ] 全部成功 [ ] 部分失败 [ ] 全部失败

问题描述:
(如果有问题，请详细描述)

控制台日志:
(请粘贴相关的控制台日志)
```

## 🎉 成功案例

如果一切正常，你应该看到：

1. **应用启动时**：
   - 主窗口正常显示
   - 2秒后自动打开开发者工具
   - 显示调试模式确认对话框

2. **控制台日志**：
   ```
   🐛 [DEBUG] 生产环境启用开发者工具 (调试模式)
   🔧 [DEBUG] 开发者工具快捷键注册结果:
     - F12: ✅
     - Ctrl+Shift+I: ✅
     - Ctrl+Shift+J: ✅
   🎉 调试模式已启用！
   ```

3. **快捷键响应**：
   - 每次按快捷键都有对应的日志输出
   - 开发者工具能够正常切换显示状态

---

**创建时间**: 2025-08-01  
**适用版本**: MEEA-VIOFO v25.07.18-1805+ (调试版本)  
**测试目标**: 验证开发者工具快捷键功能
