#!/usr/bin/env node

/**
 * macOS ARM64 构建清理脚本
 * 确保只包含 ARM64 相关的资源，移除不必要的 x64 依赖
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 开始清理 macOS ARM64 构建资源...');

// 需要清理的目录和文件模式
const cleanupPatterns = [
  // FFmpeg x64 版本
  'ffmpeg/mac-x64',
  'ffmpeg/win-*',
  'ffmpeg/linux-*',
  
  // Node.js 原生模块的 x64 版本
  'node_modules/**/prebuilds/darwin-x64',
  'node_modules/**/bin/darwin-x64',
  'node_modules/**/lib/darwin-x64',
  
  // Electron 预构建的 x64 版本
  'node_modules/electron/dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/crashpad_handler',
  
  // 其他平台的二进制文件
  'node_modules/**/bin/win32-*',
  'node_modules/**/bin/linux-*',
  'node_modules/**/prebuilds/win32-*',
  'node_modules/**/prebuilds/linux-*',
];

// 递归删除目录
function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ 已删除: ${dirPath}`);
      return true;
    } catch (error) {
      console.warn(`⚠️  无法删除 ${dirPath}: ${error.message}`);
      return false;
    }
  }
  return false;
}

// 使用 glob 模式匹配文件
function matchGlobPattern(pattern, basePath = '.') {
  const fullPattern = path.resolve(basePath, pattern);
  const parts = pattern.split('/');
  
  function searchRecursive(currentPath, patternParts, index = 0) {
    if (index >= patternParts.length) {
      return [currentPath];
    }
    
    const currentPart = patternParts[index];
    const results = [];
    
    if (currentPart.includes('*')) {
      // 处理通配符
      if (!fs.existsSync(currentPath)) return results;
      
      try {
        const entries = fs.readdirSync(currentPath, { withFileTypes: true });
        const regex = new RegExp('^' + currentPart.replace(/\*/g, '.*') + '$');
        
        for (const entry of entries) {
          if (regex.test(entry.name)) {
            const nextPath = path.join(currentPath, entry.name);
            if (index === patternParts.length - 1) {
              // 最后一个部分，直接添加
              results.push(nextPath);
            } else {
              // 继续递归
              results.push(...searchRecursive(nextPath, patternParts, index + 1));
            }
          }
        }
      } catch (error) {
        // 忽略权限错误等
      }
    } else {
      // 精确匹配
      const nextPath = path.join(currentPath, currentPart);
      if (index === patternParts.length - 1) {
        if (fs.existsSync(nextPath)) {
          results.push(nextPath);
        }
      } else {
        results.push(...searchRecursive(nextPath, patternParts, index + 1));
      }
    }
    
    return results;
  }
  
  return searchRecursive('.', parts);
}

// 执行清理
let totalCleaned = 0;
let totalSize = 0;

for (const pattern of cleanupPatterns) {
  console.log(`🔍 搜索模式: ${pattern}`);
  
  const matches = matchGlobPattern(pattern);
  
  for (const match of matches) {
    if (fs.existsSync(match)) {
      try {
        const stats = fs.statSync(match);
        if (stats.isDirectory()) {
          // 计算目录大小
          function getDirSize(dirPath) {
            let size = 0;
            try {
              const entries = fs.readdirSync(dirPath, { withFileTypes: true });
              for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                if (entry.isDirectory()) {
                  size += getDirSize(fullPath);
                } else {
                  const fileStats = fs.statSync(fullPath);
                  size += fileStats.size;
                }
              }
            } catch (error) {
              // 忽略错误
            }
            return size;
          }
          
          const dirSize = getDirSize(match);
          totalSize += dirSize;
          
          if (removeDirectory(match)) {
            totalCleaned++;
            console.log(`   📦 节省空间: ${(dirSize / 1024 / 1024).toFixed(2)} MB`);
          }
        } else {
          totalSize += stats.size;
          fs.unlinkSync(match);
          console.log(`✅ 已删除文件: ${match}`);
          console.log(`   📦 节省空间: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
          totalCleaned++;
        }
      } catch (error) {
        console.warn(`⚠️  无法处理 ${match}: ${error.message}`);
      }
    }
  }
}

console.log('\n🎉 清理完成!');
console.log(`📊 总计清理: ${totalCleaned} 个项目`);
console.log(`💾 节省空间: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);

// 验证关键的 ARM64 资源是否存在
console.log('\n🔍 验证 ARM64 资源...');

const requiredARM64Resources = [
  'ffmpeg/mac-arm64',
];

let allResourcesPresent = true;

for (const resource of requiredARM64Resources) {
  if (fs.existsSync(resource)) {
    console.log(`✅ ${resource} - 存在`);
  } else {
    console.log(`❌ ${resource} - 缺失`);
    allResourcesPresent = false;
  }
}

if (allResourcesPresent) {
  console.log('\n✅ 所有必需的 ARM64 资源都已就位!');
} else {
  console.log('\n❌ 某些 ARM64 资源缺失，请检查构建配置!');
  process.exit(1);
}
